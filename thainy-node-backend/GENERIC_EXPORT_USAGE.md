# Generic Export Utility - Usage Guide

## Overview

A simple, reusable export utility that can be used by any controller to export data in PDF, CSV, or JSON formats. No APIs needed - just import and use!

## Installation

The required dependencies are already installed:
- `pdfkit` - For PDF generation
- `csv-writer` - For CSV export
- `moment` - For date formatting

## Basic Usage

### Import the utility

```javascript
const { exportData, exportAndSend, formatData } = require('../utils/genericExport');
```

### Export to different formats

```javascript
// Export to JSON
const jsonData = await exportData(patients, { format: 'json' });

// Export to CSV
const csvData = await exportData(patients, { 
  format: 'csv',
  fields: ['TNR', 'fullname', 'DOB', 'hospital']
});

// Export to PDF
const pdfBuffer = await exportData(patients, { 
  format: 'pdf',
  title: 'Patient List Report'
});
```

## Controller Examples

### Example 1: Export Patients in Controller

```javascript
const { exportAndSend } = require('../utils/genericExport');

async function exportPatients(req, res) {
  try {
    const { format = 'csv' } = req.query;
    
    // Get your data (example)
    const patients = await db.Patient.findAll({
      where: { hospital: req.user.hospital }
    });

    // Convert to plain objects
    const patientData = patients.map(p => p.toJSON());

    // Export and send
    await exportAndSend(res, patientData, {
      format,
      filename: `patients_${req.user.hospital}.${format}`,
      title: 'Patient List',
      fields: ['TNR', 'fullname', 'DOB', 'sex', 'hospital', 'status']
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

### Example 2: Export Patient Sections (Similar to your PHP)

```javascript
const { exportData } = require('../utils/genericExport');

async function exportPatientSections(req, res) {
  try {
    const { TNR } = req.params;
    const hospital = req.user.hospital;

    // Get patient data
    const patient = await db.Patient.findOne({ where: { TNR, hospital } });
    const section1 = await db.Section1.findOne({ where: { TNR, hospital } });
    const section2 = await db.Section2.findOne({ where: { TNR, hospital } });
    const section3 = await db.Section3.findOne({ where: { TNR, hospital } });
    const section4 = await db.Section4.findOne({ where: { TNR, hospital } });

    // Prepare data for PDF
    const reportData = {
      patient: patient ? patient.toJSON() : null,
      section1: section1 ? section1.toJSON() : null,
      section2: section2 ? section2.toJSON() : null,
      section3: section3 ? section3.toJSON() : null,
      section4: section4 ? section4.toJSON() : null
    };

    // Generate PDF
    const pdfBuffer = await exportData(reportData, {
      format: 'pdf',
      title: `ThaiNy Registry (TN) - Data Record Form - TNR: ${TNR}`
    });

    // Send PDF
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="patient_${TNR}_report.pdf"`);
    res.send(pdfBuffer);

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

### Example 3: Export with Filtering

```javascript
const { exportAndSend, formatData } = require('../utils/genericExport');

async function exportFilteredPatients(req, res) {
  try {
    const { status, dateFrom, dateTo, format = 'csv' } = req.query;
    
    // Build where clause
    const whereClause = { hospital: req.user.hospital };
    if (status) whereClause.status = status;
    if (dateFrom || dateTo) {
      whereClause.created_date = {};
      if (dateFrom) whereClause.created_date[db.Sequelize.Op.gte] = dateFrom;
      if (dateTo) whereClause.created_date[db.Sequelize.Op.lte] = dateTo;
    }

    // Get filtered data
    const patients = await db.Patient.findAll({ where: whereClause });
    const patientData = patients.map(p => p.toJSON());

    // Format data with custom field mapping
    const formattedData = formatData(patientData, [
      'TNR',
      'fullname',
      { key: 'DOB', label: 'Date of Birth', format: 'date' },
      'sex',
      'hospital',
      'status',
      { key: 'created_date', label: 'Created', format: 'datetime' }
    ]);

    // Export
    await exportAndSend(res, formattedData, {
      format,
      filename: `filtered_patients_${moment().format('YYYY-MM-DD')}.${format}`,
      title: 'Filtered Patient Report'
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

## Advanced Usage

### Custom PDF Generation

```javascript
const { exportToPDF } = require('../utils/genericExport');

async function generateCustomReport(data) {
  const pdfBuffer = await exportToPDF(data, {
    title: 'Custom Medical Report',
    // PDF will automatically format the data
  });
  
  return pdfBuffer;
}
```

### Save to File Instead of Sending

```javascript
const { exportData, saveToFile } = require('../utils/genericExport');

async function saveExportToFile(data, format) {
  // Export data
  const exportedData = await exportData(data, { format });
  
  // Save to file
  const filePath = await saveToFile(exportedData, `report_${Date.now()}`, format);
  
  return filePath;
}
```

## API Functions

### `exportData(data, options)`
Main export function that returns the exported data.

**Parameters:**
- `data` - Array or Object to export
- `options` - Export options
  - `format` - 'pdf', 'csv', or 'json'
  - `title` - Title for PDF
  - `fields` - Array of fields for CSV
  - `filename` - Custom filename

**Returns:** Buffer (PDF) or String (CSV/JSON)

### `exportAndSend(res, data, options)`
Export and send directly as HTTP response.

**Parameters:**
- `res` - Express response object
- `data` - Data to export
- `options` - Same as exportData options

### `formatData(data, fieldMapping)`
Format data before export.

**Parameters:**
- `data` - Raw data
- `fieldMapping` - Array of field mappings

**Field Mapping Examples:**
```javascript
[
  'TNR',  // Simple field
  { key: 'DOB', label: 'Date of Birth', format: 'date' },  // Custom label and format
  { key: 'created_date', label: 'Created', format: 'datetime' }
]
```

## Integration with Existing Controllers

You can easily add export functionality to any existing controller:

```javascript
// Add to your existing patient controller
const { exportAndSend } = require('../utils/genericExport');

// Add this route handler
async function exportPatientsData(req, res) {
  try {
    // Use your existing data fetching logic
    const patients = await fetchAllPatients(); // your existing function
    
    // Export using the utility
    await exportAndSend(res, patients, {
      format: req.query.format || 'csv',
      title: 'Patient Export',
      fields: ['TNR', 'fullname', 'DOB', 'sex', 'hospital', 'status']
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

## Error Handling

The utility includes built-in error handling and logging:

```javascript
try {
  const result = await exportData(data, options);
  // Handle success
} catch (error) {
  // Error is automatically logged
  console.error('Export failed:', error.message);
}
```

## File Formats

### PDF
- Automatically formatted tables for arrays
- Object properties listed for single objects
- Includes title and timestamp
- Handles pagination for large datasets

### CSV
- Requires array data
- Customizable field selection
- Proper escaping of commas and quotes
- Headers included

### JSON
- Works with any data type
- Includes metadata (export date, record count)
- Pretty formatted (2-space indentation)

This utility provides everything you need for generic export functionality without creating unnecessary APIs!
