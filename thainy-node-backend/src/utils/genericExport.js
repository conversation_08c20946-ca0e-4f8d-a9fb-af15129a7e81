const PDFDocument = require('pdfkit');
const { createObjectCsvWriter } = require('csv-writer');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const logger = require('./logger');

/**
 * Generic Export Utility
 * Simple utility functions that can be used by any controller to export data
 * Supports PDF, CSV, and JSON formats
 */

/**
 * Export data to specified format
 * @param {Array|Object} data - Data to export
 * @param {Object} options - Export options
 * @param {string} options.format - Export format ('pdf', 'csv', 'json')
 * @param {string} options.filename - Output filename (optional)
 * @param {string} options.title - Title for the export (optional)
 * @param {Array} options.fields - Fields to include (for CSV)
 * @param {Object} options.pdfOptions - PDF-specific options
 * @returns {Promise<Buffer|string>} Export result
 */
async function exportData(data, options = {}) {
  const {
    format = 'json',
    filename,
    title = 'Data Export',
    fields = [],
    pdfOptions = {}
  } = options;

  try {
    switch (format.toLowerCase()) {
      case 'pdf':
        return await exportToPDF(data, { title, filename, ...pdfOptions });
      case 'csv':
        return await exportToCSV(data, { fields, filename });
      case 'json':
        return await exportToJSON(data, { filename });
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  } catch (error) {
    logger.error('Export failed:', error);
    throw error;
  }
}

/**
 * Export data to PDF format
 * @param {Array|Object} data - Data to export
 * @param {Object} options - PDF options
 * @returns {Promise<Buffer>} PDF buffer
 */
async function exportToPDF(data, options = {}) {
  const { title = 'Data Export', filename } = options;

  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      // Collect PDF data
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve(pdfBuffer);
      });
      doc.on('error', reject);

      // Generate PDF content
      generatePDFContent(doc, data, title);
      doc.end();

    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Generate PDF content based on data type
 * @param {PDFDocument} doc - PDF document
 * @param {Array|Object} data - Data to render
 * @param {string} title - Document title
 */
function generatePDFContent(doc, data, title) {
  // Header
  doc.fontSize(16).font('Helvetica-Bold');
  doc.text(title, { align: 'center' });
  doc.moveDown();

  doc.fontSize(12).font('Helvetica');
  doc.text(`Generated: ${moment().format('YYYY-MM-DD HH:mm:ss')}`, { align: 'center' });
  doc.moveDown(2);

  // Content
  if (Array.isArray(data)) {
    generatePDFTable(doc, data);
  } else if (typeof data === 'object') {
    generatePDFObject(doc, data);
  } else {
    doc.fontSize(10).text(String(data));
  }
}

/**
 * Generate PDF table for array data
 * @param {PDFDocument} doc - PDF document
 * @param {Array} data - Array data
 */
function generatePDFTable(doc, data) {
  if (data.length === 0) {
    doc.text('No data available');
    return;
  }

  doc.fontSize(10).font('Helvetica');
  doc.text(`Total Records: ${data.length}`);
  doc.moveDown();

  // Get headers from first object
  const headers = Object.keys(data[0]);
  const colWidth = Math.min(80, (doc.page.width - 100) / headers.length);

  // Table headers
  doc.font('Helvetica-Bold');
  let x = 50;
  headers.forEach(header => {
    doc.text(header, x, doc.y, { width: colWidth });
    x += colWidth;
  });
  doc.moveDown();

  // Table data
  doc.font('Helvetica');
  data.forEach((row, index) => {
    x = 50;
    headers.forEach(header => {
      const value = row[header] || '';
      doc.text(String(value).substring(0, 15), x, doc.y, { width: colWidth });
      x += colWidth;
    });
    doc.moveDown();

    // Add new page if needed
    if (doc.y > 700) {
      doc.addPage();
    }

    // Limit to prevent huge PDFs
    if (index > 100) {
      doc.text('... (showing first 100 records)', 50);
      return;
    }
  });
}

/**
 * Generate PDF content for single object
 * @param {PDFDocument} doc - PDF document
 * @param {Object} data - Object data
 */
function generatePDFObject(doc, data) {
  doc.fontSize(10).font('Helvetica');
  
  Object.entries(data).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      doc.text(`${key}: ${String(value)}`);
    }
  });
}

/**
 * Export data to CSV format
 * @param {Array} data - Data to export (must be array)
 * @param {Object} options - CSV options
 * @returns {Promise<string>} CSV string
 */
async function exportToCSV(data, options = {}) {
  const { fields = [], filename } = options;

  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('CSV export requires non-empty array data');
  }

  // Determine fields
  const csvFields = fields.length > 0 ? fields : Object.keys(data[0]);

  // Create CSV content
  const csvHeader = csvFields.join(',');
  const csvRows = data.map(row => {
    return csvFields.map(field => {
      const value = row[field] || '';
      // Escape commas and quotes
      const escaped = String(value).replace(/"/g, '""');
      return `"${escaped}"`;
    }).join(',');
  });

  const csvContent = [csvHeader, ...csvRows].join('\n');
  return csvContent;
}

/**
 * Export data to JSON format
 * @param {Array|Object} data - Data to export
 * @param {Object} options - JSON options
 * @returns {Promise<string>} JSON string
 */
async function exportToJSON(data, options = {}) {
  const exportData = {
    exportDate: moment().toISOString(),
    recordCount: Array.isArray(data) ? data.length : 1,
    data: data
  };

  return JSON.stringify(exportData, null, 2);
}

/**
 * Save export to file
 * @param {Buffer|string} exportData - Export data
 * @param {string} filename - Filename
 * @param {string} format - Format
 * @returns {Promise<string>} File path
 */
async function saveToFile(exportData, filename, format) {
  const tempDir = path.join(__dirname, '../../temp');
  
  // Ensure temp directory exists
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  const finalFilename = filename || `export_${timestamp}.${format}`;
  const filePath = path.join(tempDir, finalFilename);

  if (format === 'pdf') {
    fs.writeFileSync(filePath, exportData);
  } else {
    fs.writeFileSync(filePath, exportData, 'utf8');
  }

  return filePath;
}

/**
 * Export and send as HTTP response
 * @param {Object} res - Express response object
 * @param {Array|Object} data - Data to export
 * @param {Object} options - Export options
 */
async function exportAndSend(res, data, options = {}) {
  const { format = 'json', filename } = options;

  try {
    const exportedData = await exportData(data, options);
    const contentType = getContentType(format);
    const finalFilename = filename || `export.${format}`;

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${finalFilename}"`);

    res.send(exportedData);

  } catch (error) {
    logger.error('Export and send failed:', error);
    res.status(500).json({
      success: false,
      message: 'Export failed',
      error: error.message
    });
  }
}

/**
 * Get content type for format
 * @param {string} format - Export format
 * @returns {string} Content type
 */
function getContentType(format) {
  const contentTypes = {
    pdf: 'application/pdf',
    csv: 'text/csv',
    json: 'application/json'
  };
  
  return contentTypes[format.toLowerCase()] || 'application/octet-stream';
}

/**
 * Format data for export (helper function)
 * @param {Array|Object} data - Raw data
 * @param {Array} fieldMapping - Field mapping array
 * @returns {Array|Object} Formatted data
 */
function formatData(data, fieldMapping = []) {
  if (!fieldMapping.length) return data;

  if (Array.isArray(data)) {
    return data.map(item => formatSingleItem(item, fieldMapping));
  } else {
    return formatSingleItem(data, fieldMapping);
  }
}

/**
 * Format single data item
 * @param {Object} item - Data item
 * @param {Array} fieldMapping - Field mapping
 * @returns {Object} Formatted item
 */
function formatSingleItem(item, fieldMapping) {
  const formatted = {};
  
  fieldMapping.forEach(mapping => {
    if (typeof mapping === 'string') {
      formatted[mapping] = item[mapping];
    } else if (typeof mapping === 'object') {
      const { key, label, format } = mapping;
      formatted[label || key] = formatValue(item[key], format);
    }
  });

  return formatted;
}

/**
 * Format individual value
 * @param {any} value - Value to format
 * @param {string} format - Format type
 * @returns {any} Formatted value
 */
function formatValue(value, format) {
  if (value === null || value === undefined) return 'N/A';

  switch (format) {
    case 'date':
      return moment(value).format('YYYY-MM-DD');
    case 'datetime':
      return moment(value).format('YYYY-MM-DD HH:mm:ss');
    case 'currency':
      return `$${parseFloat(value).toFixed(2)}`;
    default:
      return value;
  }
}

module.exports = {
  exportData,
  exportToPDF,
  exportToCSV,
  exportToJSON,
  saveToFile,
  exportAndSend,
  formatData
};
