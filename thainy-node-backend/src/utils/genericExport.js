const PDFDocument = require('pdfkit');
const { createObjectCsvWriter } = require('csv-writer');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const logger = require('./logger');

/**
 * Generic Export Utility
 * Simple utility functions that can be used by any controller to export data
 * Supports PDF, CSV, and JSON formats
 */

/**
 * Export data to specified format
 * @param {Array|Object} data - Data to export
 * @param {Object} options - Export options
 * @param {string} options.format - Export format ('pdf', 'csv', 'json')
 * @param {string} options.filename - Output filename (optional)
 * @param {string} options.title - Title for the export (optional)
 * @param {Array} options.fields - Fields to include (for CSV)
 * @param {Object} options.pdfOptions - PDF-specific options
 * @returns {Promise<Buffer|string>} Export result
 */
async function exportData(data, options = {}) {
  const {
    format = 'json',
    filename,
    title = 'Data Export',
    fields = [],
    pdfOptions = {}
  } = options;

  try {
    switch (format.toLowerCase()) {
      case 'pdf':
        return await exportToPDF(data, { title, filename, ...pdfOptions });
      case 'csv':
        return await exportToCSV(data, { fields, filename });
      case 'json':
        return await exportToJSON(data, { filename });
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  } catch (error) {
    logger.error('Export failed:', error);
    throw error;
  }
}

/**
 * Export data to PDF format
 * @param {Array|Object} data - Data to export
 * @param {Object} options - PDF options
 * @returns {Promise<Buffer>} PDF buffer
 */
async function exportToPDF(data, options = {}) {
  const { title = 'Data Export', filename } = options;

  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      // Collect PDF data
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve(pdfBuffer);
      });
      doc.on('error', reject);

      // Generate PDF content
      generatePDFContent(doc, data, title);
      doc.end();

    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Generate PDF content based on data type - HANDLES PATIENT EXPORT STRUCTURE
 * @param {PDFDocument} doc - PDF document
 * @param {Array|Object} data - Data to render
 * @param {string} title - Document title
 */
function generatePDFContent(doc, data, title) {
  // Check if this is patient export data structure
  if (data && data.header && data.patient && data.sections) {
    generatePatientExportPDF(doc, data);
  } else {
    // Generic PDF generation for other data
    generateGenericPDF(doc, data, title);
  }
}

/**
 * Generate PDF specifically for patient export (like your PHP structure)
 * @param {PDFDocument} doc - PDF document
 * @param {Object} data - Patient export data
 */
function generatePatientExportPDF(doc, data) {
  const { header, patient, sections } = data;

  // Header - exactly like your PHP headerTable()
  doc.fontSize(16).font('Helvetica-Bold');
  doc.text(header.title || 'ThaiNy Registry (TN) - Data Record Form', { align: 'center' });
  doc.moveDown();

  doc.fontSize(14).font('Helvetica-Bold');
  doc.text(`TN Number: ${header.tnr || patient?.TNR || 'N/A'}`, { align: 'center' });
  doc.moveDown(2);

  // Add patient information section (this was missing!)
  if (patient) {
    generatePatientInfoSection(doc, patient);
    doc.addPage();
  }

  // Generate sections - exactly like your PHP section methods
  Object.entries(sections).forEach(([sectionName, sectionData]) => {
    if (sectionData) {
      generatePatientSection(doc, sectionName, sectionData);
      doc.addPage(); // New page for each section like your PHP
    }
  });
}

/**
 * Generate patient information section (basic patient data)
 * @param {PDFDocument} doc - PDF document
 * @param {Object} patient - Patient data
 */
function generatePatientInfoSection(doc, patient) {
  // Patient Info header with border
  doc.fontSize(14).font('Helvetica-Bold');
  doc.rect(50, doc.y, 500, 20).stroke();
  doc.text('PATIENT INFORMATION', 60, doc.y - 15);
  doc.moveDown(2);

  doc.fontSize(10).font('Helvetica');

  // Display patient information in a structured way
  doc.text(`TNR: ${patient.TNR || 'N/A'}`);
  doc.text(`Hospital: ${patient.hospital || 'N/A'}`);
  doc.text(`Full Name: ${patient.fullname || 'N/A'}`);
  doc.text(`HN: ${patient.HN || 'N/A'}`);
  doc.moveDown(0.5);

  doc.text(`Infant ID: ${patient.infant_id || 'N/A'}`);
  doc.text(`Passport ID: ${patient.passport_id || 'N/A'}`);
  doc.text(`Date of Birth: ${patient.DOB || 'N/A'}`);
  doc.text(`Sex: ${patient.sex || 'N/A'}`);
  doc.text(`Ethnic: ${patient.ethnic || 'N/A'}`);
  doc.moveDown(0.5);

  doc.font('Helvetica-Bold');
  doc.text('Mother Information:');
  doc.font('Helvetica');
  doc.text(`Mother Full Name: ${patient.mother_fullname || 'N/A'}`);
  doc.text(`Mother ID: ${patient.mother_id || 'N/A'}`);
  doc.text(`Mother Passport: ${patient.mother_passport || 'N/A'}`);
  doc.text(`Mother Address: ${patient.mother_address || 'N/A'}`);
  doc.text(`Mother Tel: ${patient.mother_tel || 'N/A'}`);
  doc.moveDown(0.5);

  doc.font('Helvetica-Bold');
  doc.text('Contact Information:');
  doc.font('Helvetica');
  doc.text(`Contact Person Name: ${patient.contact_person_name || 'N/A'}`);
  doc.text(`Relation: ${patient.relation || 'N/A'}`);
  doc.text(`Contact Person Tel: ${patient.contact_person_tel || 'N/A'}`);
  doc.text(`Other Contact: ${patient.other_contact || 'N/A'}`);
  doc.moveDown(0.5);

  doc.text(`Status: ${patient.status || 'N/A'}`);
  doc.text(`Created Date: ${patient.created_date || 'N/A'}`);
}

/**
 * Generate individual patient section (like your PHP section1(), section2(), etc.)
 * @param {PDFDocument} doc - PDF document
 * @param {string} sectionName - Section name
 * @param {Object} sectionData - Section data
 */
function generatePatientSection(doc, sectionName, sectionData) {
  // Section header with border (like your PHP Cell with border)
  doc.fontSize(14).font('Helvetica-Bold');
  doc.rect(50, doc.y, 500, 20).stroke();
  doc.text(sectionName.toUpperCase(), 60, doc.y - 15);
  doc.moveDown(2);

  doc.fontSize(10).font('Helvetica');

  switch (sectionName.toLowerCase()) {
    case 'section1':
      generateSection1PDF(doc, sectionData);
      break;
    case 'section2':
      generateSection2PDF(doc, sectionData);
      break;
    case 'section3':
      generateSection3PDF(doc, sectionData);
      break;
    case 'section4':
      generateSection4PDF(doc, sectionData);
      break;
    default:
      generateGenericSectionPDF(doc, sectionData);
  }
}

/**
 * Generate Section 1 PDF (exactly like your PHP section1())
 */
function generateSection1PDF(doc, data) {
  if (!data) return;

  doc.text(`Birth status: ${data.admission_status || 'N/A'}`);
  doc.moveDown(0.5);

  // Indented items (like your PHP with empty cells for indentation)
  doc.text(`    - Intrauterine transfer: ${data.intrauterine_transter || 'N/A'}`);
  doc.text(`    - Transfer member: ${data.transfer_member || 'N/A'}`);
  doc.text(`    - Hospital name: ${data.hospital_name || 'N/A'}`);
  doc.moveDown(0.5);

  doc.text(`Date of Admission in our center: ${data.admission_date || 'N/A'}`);
  doc.text(`Date of Discharge in our center: ${data.discharge_date || 'N/A'}`);
  doc.text(`Hospital stay in our center: ${data.stay_day || 'N/A'}`);
  doc.text(`Status at discharge from our center: ${data.discharge_type || 'N/A'}`);
}

/**
 * Generate Section 2 PDF (like your PHP section2() with multiple queries)
 */
function generateSection2PDF(doc, data) {
  if (!data) return;

  // Maternal age (like your PHP age handling)
  const age = data.age_NA === 'NA' ? 'N/A' : (data.age || 'N/A');
  doc.text(`Maternal age: ${age}`);
  doc.moveDown(0.5);

  // G, P, A on same line (like your PHP)
  const g = data.G_NA === 'NA' ? 'N/A' : (data.G || 'N/A');
  const p = data.P_NA === 'NA' ? 'N/A' : (data.P || 'N/A');
  const a = data.A_NA === 'NA' ? 'N/A' : (data.A || 'N/A');
  doc.text(`G: ${g}    P: ${p}    A: ${a}`);
  doc.moveDown(0.5);

  // Handle related data (like your PHP multiple queries)
  if (data.abnormal_serology) {
    const serology = formatSection2MultipleFields(data.abnormal_serology, [
      'positive_anti_HIV', 'positive_HBsAg', 'positive_VDRL', 'normal_all', 'other'
    ]);
    doc.text(`Abnormal serology: ${serology}`);
  }

  if (data.complications) {
    const complications = formatSection2MultipleFields(data.complications, [
      'no_ANC', 'over_DM_or_GDM', 'chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia',
      'meternal_thyroid_disease', 'multiple_gestation', 'meternal_UTI', 'meternal_drug_abuse', 'other'
    ]);
    doc.text(`Complication during pregnancy: ${complications}`);
  }

  // Add other section2 related data...
}

/**
 * Format section2 multiple fields (like your PHP text concatenation)
 */
function formatSection2MultipleFields(data, fields) {
  if (!data || data.NA === 'N/A') return 'N/A';

  const values = [];
  fields.forEach(field => {
    if (data[field] && data[field] !== '') {
      values.push(data[field]);
    }
  });

  return values.length > 0 ? values.join(', ') : 'N/A';
}

/**
 * Generate Section 3 PDF (like your PHP section3())
 */
function generateSection3PDF(doc, data) {
  if (!data) return;

  doc.text(`Mode of delivery: ${data.delivery_mode || 'N/A'}`);
  doc.moveDown(0.5);

  // Apgar scores on same line
  doc.text(`Apgar score: 1 Min: ${data['1_min_score'] || 'N/A'}, 5 Min: ${data['5_min_score'] || 'N/A'}, 10 Min: ${data['10_min_score'] || 'N/A'}`);
  doc.moveDown(0.5);

  // Resuscitation (like your PHP conditional logic)
  if (data.resuscitation === 'No') {
    doc.text('Resuscitation: No');
  } else {
    const resuscitation = formatSection2MultipleFields(data, ['CPAP', 'PPV', 'intubation', 'chest_compression', 'epinephrine']);
    doc.text(`Resuscitation: ${resuscitation}`);
  }
  doc.moveDown(0.5);

  // Cord blood (like your PHP conditional logic)
  if (data.cord_blood === 'Not done') {
    doc.text('Cord blood / 1st hour blood gas: Not done');
  } else {
    const cordBlood = formatSection2MultipleFields(data, ['cord_blood_pH', 'cord_blood_pCO2', 'cord_blood_HCO2', 'cord_blood_BE']);
    doc.text(`Cord blood / 1st hour blood gas: ${cordBlood}`);
  }
  doc.moveDown(0.5);

  doc.text(`Delayed cord clamping / Milking: ${data.delayed_cord_clamping || 'N/A'}`);
}

/**
 * Generate Section 4 PDF (like your PHP section4())
 */
function generateSection4PDF(doc, data) {
  if (!data) return;

  doc.text(`Date of birth: ${data.DOB || 'N/A'}    Time of birth: ${data.TOB || 'N/A'}`);
  doc.moveDown(0.5);

  doc.text(`Gender: ${data.gender || 'N/A'}`);
  doc.moveDown(0.5);

  doc.text(`Gestational age: ${data.gestational_age_week || 0} week(s) ${data.gestational_age_day || 0} day(s)`);
  doc.moveDown(0.5);

  doc.text(`Birth weight: ${data.birth_weight || 'N/A'}`);
  doc.text(`Length: ${data.length || 'N/A'}`);
  doc.text(`Head circumference: ${data.head_circumference || 'N/A'}`);
  doc.text(`Growth status (Fenton 2013): ${data.growth_status || 'N/A'}`);
}

/**
 * Generate generic section PDF for unknown sections
 */
function generateGenericSectionPDF(doc, data) {
  if (!data) return;

  Object.entries(data).forEach(([key, value]) => {
    if (key !== 'TNR' && key !== 'hospital' && value !== null && value !== '') {
      doc.text(`${key}: ${value}`);
    }
  });
}

/**
 * Generate generic PDF for non-patient data
 */
function generateGenericPDF(doc, data, title) {
  // Header
  doc.fontSize(16).font('Helvetica-Bold');
  doc.text(title, { align: 'center' });
  doc.moveDown();

  doc.fontSize(12).font('Helvetica');
  doc.text(`Generated: ${moment().format('YYYY-MM-DD HH:mm:ss')}`, { align: 'center' });
  doc.moveDown(2);

  // Generic content generation
  generateGenericPDFContent(doc, data);
}

/**
 * Generic PDF content generator - handles any data structure
 * @param {PDFDocument} doc - PDF document
 * @param {any} data - Data to render
 * @param {number} indent - Indentation level
 */
function generateGenericPDFContent(doc, data, indent = 0) {
  const indentSize = 20;
  const leftMargin = 50 + (indent * indentSize);

  if (Array.isArray(data)) {
    // Handle arrays
    if (data.length === 0) {
      doc.fontSize(10).text('No data available', leftMargin);
      return;
    }

    // If array of objects, create table-like format
    if (typeof data[0] === 'object') {
      doc.fontSize(10).font('Helvetica');
      doc.text(`Total Records: ${data.length}`, leftMargin);
      doc.moveDown(0.5);

      data.forEach((item, index) => {
        doc.font('Helvetica-Bold');
        doc.text(`Record ${index + 1}:`, leftMargin);
        doc.font('Helvetica');
        generateGenericPDFContent(doc, item, indent + 1);
        doc.moveDown(0.5);

        // Prevent huge PDFs
        if (index > 50) {
          doc.text(`... (showing first 50 records)`, leftMargin);
          return;
        }

        // Add new page if needed
        if (doc.y > 700) {
          doc.addPage();
        }
      });
    } else {
      // Array of simple values
      data.forEach((item, index) => {
        doc.fontSize(10).text(`${index + 1}. ${String(item)}`, leftMargin);
      });
    }

  } else if (typeof data === 'object' && data !== null) {
    // Handle objects
    Object.entries(data).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        doc.fontSize(10).text(`${key}: N/A`, leftMargin);
      } else if (typeof value === 'object') {
        // Nested object or array
        doc.fontSize(10).font('Helvetica-Bold');
        doc.text(`${key}:`, leftMargin);
        doc.font('Helvetica');
        generateGenericPDFContent(doc, value, indent + 1);
      } else {
        // Simple value
        doc.fontSize(10).font('Helvetica');
        doc.text(`${key}: ${String(value)}`, leftMargin);
      }
    });

  } else {
    // Handle primitive values
    doc.fontSize(10).text(String(data), leftMargin);
  }
}



/**
 * Export data to CSV format
 * @param {Array} data - Data to export (must be array)
 * @param {Object} options - CSV options
 * @returns {Promise<string>} CSV string
 */
async function exportToCSV(data, options = {}) {
  const { fields = [] } = options;

  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('CSV export requires non-empty array data');
  }

  // Determine fields
  const csvFields = fields.length > 0 ? fields : Object.keys(data[0]);

  // Create CSV content
  const csvHeader = csvFields.join(',');
  const csvRows = data.map(row => {
    return csvFields.map(field => {
      const value = row[field] || '';
      // Escape commas and quotes
      const escaped = String(value).replace(/"/g, '""');
      return `"${escaped}"`;
    }).join(',');
  });

  const csvContent = [csvHeader, ...csvRows].join('\n');
  return csvContent;
}

/**
 * Export data to JSON format
 * @param {Array|Object} data - Data to export
 * @param {Object} options - JSON options
 * @returns {Promise<string>} JSON string
 */
async function exportToJSON(data) {
  const exportData = {
    exportDate: moment().toISOString(),
    recordCount: Array.isArray(data) ? data.length : 1,
    data: data
  };

  return JSON.stringify(exportData, null, 2);
}

/**
 * Save export to file
 * @param {Buffer|string} exportData - Export data
 * @param {string} filename - Filename
 * @param {string} format - Format
 * @returns {Promise<string>} File path
 */
async function saveToFile(exportData, filename, format) {
  const tempDir = path.join(__dirname, '../../temp');
  
  // Ensure temp directory exists
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  const finalFilename = filename || `export_${timestamp}.${format}`;
  const filePath = path.join(tempDir, finalFilename);

  if (format === 'pdf') {
    fs.writeFileSync(filePath, exportData);
  } else {
    fs.writeFileSync(filePath, exportData, 'utf8');
  }

  return filePath;
}

/**
 * Export and send as HTTP response
 * @param {Object} res - Express response object
 * @param {Array|Object} data - Data to export
 * @param {Object} options - Export options
 */
async function exportAndSend(res, data, options = {}) {
  const { format = 'json', filename } = options;

  try {
    const exportedData = await exportData(data, options);
    const contentType = getContentType(format);
    const finalFilename = filename || `export.${format}`;

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${finalFilename}"`);

    res.send(exportedData);

  } catch (error) {
    logger.error('Export and send failed:', error);
    res.status(500).json({
      success: false,
      message: 'Export failed',
      error: error.message
    });
  }
}

/**
 * Get content type for format
 * @param {string} format - Export format
 * @returns {string} Content type
 */
function getContentType(format) {
  const contentTypes = {
    pdf: 'application/pdf',
    csv: 'text/csv',
    json: 'application/json'
  };
  
  return contentTypes[format.toLowerCase()] || 'application/octet-stream';
}

/**
 * Format data for export (helper function)
 * @param {Array|Object} data - Raw data
 * @param {Array} fieldMapping - Field mapping array
 * @returns {Array|Object} Formatted data
 */
function formatData(data, fieldMapping = []) {
  if (!fieldMapping.length) return data;

  if (Array.isArray(data)) {
    return data.map(item => formatSingleItem(item, fieldMapping));
  } else {
    return formatSingleItem(data, fieldMapping);
  }
}

/**
 * Format single data item
 * @param {Object} item - Data item
 * @param {Array} fieldMapping - Field mapping
 * @returns {Object} Formatted item
 */
function formatSingleItem(item, fieldMapping) {
  const formatted = {};
  
  fieldMapping.forEach(mapping => {
    if (typeof mapping === 'string') {
      formatted[mapping] = item[mapping];
    } else if (typeof mapping === 'object') {
      const { key, label, format } = mapping;
      formatted[label || key] = formatValue(item[key], format);
    }
  });

  return formatted;
}

/**
 * Format individual value
 * @param {any} value - Value to format
 * @param {string} format - Format type
 * @returns {any} Formatted value
 */
function formatValue(value, format) {
  if (value === null || value === undefined) return 'N/A';

  switch (format) {
    case 'date':
      return moment(value).format('YYYY-MM-DD');
    case 'datetime':
      return moment(value).format('YYYY-MM-DD HH:mm:ss');
    case 'currency':
      return `$${parseFloat(value).toFixed(2)}`;
    default:
      return value;
  }
}

// SIMPLIFIED EXPORT - Just like your PHP approach
module.exports = {
  // Main method - generates export data
  exportData,

  // Direct download method - like PHP Output("S")
  exportAndSend,

  // Optional helpers (can be removed if not needed)
  saveToFile,      // Only if you want to save files on server
  formatData       // Only if you need data formatting
};
