// Utility for extracting pagination options and formatting paginated results

function getPagination(query, defaultLimit = 10, maxLimit = 100) {
  let { limit, offset, page } = query;
  limit = parseInt(limit, 10);
  offset = parseInt(offset, 10);
  page = parseInt(page, 10);

  if (isNaN(limit) || limit <= 0) limit = defaultLimit;
  if (limit > maxLimit) limit = maxLimit;

  if (!isNaN(page) && page > 0) {
    offset = (page - 1) * limit;
  }
  if (isNaN(offset) || offset < 0) offset = 0;

  return { limit, offset };
}

function getPagingData(data, page, limit) {
  const { count: totalItems, rows: results } = data;
  const currentPage = page ? +page : 1;
  const totalPages = Math.ceil(totalItems / limit);
  return { totalItems, results, totalPages, currentPage };
}

module.exports = { getPagination, getPagingData }; 