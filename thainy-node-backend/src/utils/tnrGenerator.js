const db = require('../models');

/**
 * Generates a TNR (Treatment Number Record) based on registration date
 * Mimics the exact PHP generateTNR function logic
 * @param {string} registDate - Registration date in format YYYY-MM-DD
 * @param {string} hospital - Hospital identifier (not used in TNR generation but kept for compatibility)
 * @returns {Promise<string>} Generated TNR
 */
async function generateTNR(registDate, hospital) {
  try {
    // Parse the registration date - exactly like PHP explode("-", $regist_date)
    const dateParts = registDate.split('-');
    if (dateParts.length !== 3) {
      throw new Error('Invalid registration date format');
    }

    const year = dateParts[0];
    let month = dateParts[1];

    // Add leading zero if month < 10 (exactly like PHP logic)
    if (parseInt(month, 10) < 10) {
      month = "0" + month.replace(/^0+/, ''); // Remove existing leading zeros first, then add one
    }

    const datePrefix = year + month; // YYYYMM

    // Count existing TNRs with this prefix in patient table (exactly like PHP)
    const countResult = await db.sequelize.query(
      `SELECT COUNT(*) as count FROM patient WHERE TNR LIKE '${datePrefix}%'`,
      {
        type: db.Sequelize.QueryTypes.SELECT,
      }
    );

    // Get the count and add 1 (exactly like PHP: mysql_num_rows($query)+1)
    const existingCount = countResult[0].count;
    let number = existingCount + 1;
    const num = number;

    // Pad the number to 5 digits with leading zeros (exactly like PHP logic)
    if (num < 10000) {
      number = "0" + number;
    }
    if (num < 1000) {
      number = "0" + number;
    }
    if (num < 100) {
      number = "0" + number;
    }
    if (num < 10) {
      number = "0" + number;
    }

    // Generate TNR: YYYYMM + padded number (exactly like PHP)
    const tnr = datePrefix + number;

    return tnr;
  } catch (error) {
    console.error('Error in generateTNR:', error);
    // Fallback TNR generation if database query fails
    const timestamp = Date.now().toString();
    const fallbackTNR = timestamp.slice(-11); // Last 11 digits of timestamp
    return fallbackTNR;
  }
}

module.exports = { generateTNR };
