const db = require('../models');
const logger = require('../utils/logger');

async function getReferredPatients({ destination_hospital, status }) {
  try {
    const refers = await db.Refer.findAll({
      where: {
        destination_hospital,
        status
      },
      include: [
        {
          model: db.Patient,
          as: 'patient',
          required: true,
          attributes: ['TNR', 'fullname', 'hospital']
        }
      ],
      attributes: ['refer_date'],
    });

    return refers.map(r => ({
      TNR: r.patient.TNR,
      fullname: r.patient.fullname,
      hospital: r.patient.hospital,
      refer_date: r.refer_date
    }));
  } catch (error) {
    logger.error('Error fetching referred patients:', error);
    throw error;
  }
}

module.exports = { getReferredPatients }; 