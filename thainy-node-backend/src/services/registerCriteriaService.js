const logger = require('../utils/logger');
const { addToDatabase } = require('../utils/databaseUtils');

/**
 * Creates a new register criteria record
 * @param {Object|Array} criteriaData - The criteria data to insert
 * @returns {Promise<Object>} Created register criteria record
 */
async function createRegisterCriteria(criteriaData) {
  try {
    // Use the generic addToDatabase function
    const result = await addToDatabase('register_criteria', criteriaData);

    logger.info('Successfully created register criteria:', result.toJSON());
    return result;
  } catch (error) {
    logger.error('Error creating register criteria:', error);
    throw error;
  }
}

module.exports = {
  createRegisterCriteria
};
