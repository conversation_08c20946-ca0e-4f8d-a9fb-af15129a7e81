const md5 = require('md5');
const db = require('../models');
const logger = require('../utils/logger');
const crypto = require('crypto');

async function loginUser(username, password) {
  try {
    const hashedPassword = md5(password);
    const user = await db.User.findOne({
      where: { username, password: hashedPassword },
      include: [{ model: db.Pin, as: 'pin', required: false }],
    });
    if (!user) {
      return {
        status: 0,
        message: 'User not found or invalid credentials.'
      };
    }
    if (user.status !== 'approved' && user.status !== 'ready') {
      return {
        status: 0,
        message: 'User is not validated. Current status: ' + user.status
      };
    }

    return {
      status: 1,
      pin: user.pin ? user.pin.pin : null,
      username: user.username,
      email: user.email,
      hospital: user.hospital,
      message: 'Login successful.'
    };
  } catch (error) {
    logger.error(`AuthService login error for user ${username}:`, error);
    throw error;
  }
}

async function handleForgotPassword(email) {
  try {
    const user = await db.User.findOne({ where: { email } });
    if (!user) {
      return { status: 0, message: 'No account found with that email address.' };
    }
    // Generate token
    const token = crypto.createHmac('sha256', String(Math.random()))
      .update(user.password)
      .digest('hex');
    const stored_token = crypto.createHash('sha256').update(token).digest('hex');
    const expired = String(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now (ms)

    // Upsert token
    await db.ForgetPasswordToken.upsert({
      email,
      token: stored_token,
      expired,
    });

    // For now, just log the reset link
    const url = `https://techvernity.com/thainy/php/forget_password_page.php?token=${token}`;
    logger.info(`Password reset link for ${email}: ${url}`);

    return { status: 1, message: 'Reset link generated', email, url };
  } catch (error) {
    logger.error('handleForgotPassword error:', error);
    throw new Error('Failed to process forgot password request.');
  }
}

async function checkOrChangePassword({ username, password, action }) {
  try {
    if (!username || !password || !action) {
      return { status: 0, message: 'Missing input', code: 400 };
    }
    const hashedPassword = md5(password);
    if (action === 'check') {
      const user = await db.User.findOne({ where: { username, password: hashedPassword } });
      if (!user) {
        return { status: 0, message: 'Invalid credentials', code: 401 };
      }
      return { status: 1, message: 'Password is correct' };
    } else if (action === 'change') {
      const [updated] = await db.User.update(
        { password: hashedPassword },
        { where: { username } }
      );
      if (updated === 0) {
        return { status: 0, message: 'Update failed', code: 400 };
      }
      return { status: 1, message: 'Password updated successfully' };
    } else {
      return { status: 0, message: 'Invalid action', code: 400 };
    }
  } catch (error) {
    logger.error('checkOrChangePassword error:', error);
    throw new Error('Failed to process password check/change request.');
  }
}

module.exports = { loginUser, handleForgotPassword, checkOrChangePassword }; 