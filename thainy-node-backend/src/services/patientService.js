const db = require('../models');
const logger = require('../utils/logger');
const { Op, Sequelize } = require('sequelize');
const { generateTNR } = require('../utils/tnrGenerator');
const { addToDatabase } = require('../utils/databaseUtils');

async function removeUserConcurrence(username) {
  try {
    await db.UserConcurrence.destroy({ where: { username } });
  } catch (error) {
    logger.error(`Error deleting user_concurrence for username ${username}:`, error);
    throw error;
  }
}

async function fetchAllPatients({ hospital, status, limit, offset }) {
  try {
    const { count, rows: patients } = await db.Patient.findAndCountAll({ 
      attributes: ['fullname', 'TNR'],
      where: {
        hospital,
        status: { [Op.like]: `${status}%` },
      },
      include: [
        {
          model: db.RegisterCriteria,
          as: 'register_criteria',
          required: true,
          attributes: [],
        },
        {
          model: db.SectionProgress,
          as: 'section_progress',
          required: true,
          attributes: [],
          where: { hospital },
        },
      ],
      raw: true,
      limit,
      offset
    });

    const referStatuses = await db.sequelize.query(
      `SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = ? ORDER BY refer_id DESC) AS referx GROUP BY TNR`,
      {
        replacements: [hospital],
        type: Sequelize.QueryTypes.SELECT,
      }
    );
    const referStatusMap = {};
    referStatuses.forEach(row => {
      referStatusMap[row.TNR] = row.status;
    });

    const result = patients.map(p => ({
      fullname: p.fullname,
      TNR: p.TNR,
      refer_status: referStatusMap[p.TNR] || null,
    }));
    return { count, rows: result };
  } catch (error) {
    logger.error(`Error fetching patients for hospital ${hospital} and status ${status}:`, error);
    throw error;
  }
}

/**
 * Filters patients based on dynamic criteria, including symptoms, date, completion, and status.
 * @param {string} hospital - Hospital identifier
 * @param {object} filters - Filtering options from request body
 * @returns {Promise<Array>} Filtered patient list with refer status
 */
async function filterPatientsByCriteria(hospital, filters, limit, offset) {
  try {
    const where = { hospital };
    const criteriaWhere = {};
    const progressWhere = { hospital };

    // Date filters
    if (filters.date_start) {
      where.created_date = { [Op.gte]: filters.date_start };
    }
    if (filters.date_stop) {
      where.created_date = { ...(where.created_date || {}), [Op.lte]: filters.date_stop };
    }

    // Symptoms filter
    if (filters.GA || filters.BW || filters.HIE || filters.major) {
      criteriaWhere[Op.or] = [];
      if (filters.GA) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%GA%' } });
      if (filters.BW) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%BW%' } });
      if (filters.HIE) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%HIE%' } });
      if (filters.major) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%Major%' } });
    }

    // Completion filter
    const completeSections = [
      'section1', 'section2', 'section3', 'section4', 'section5_1', 'section5_2', 'section5_3',
      'section5_4', 'section5_5', 'section5_6', 'section5_7', 'section5_8', 'section5_9',
      'section5_10', 'section5_11', 'section5_12'
    ];
    if (filters.complete === 'true' || filters.incomplete === 'true') {
      if (filters.complete === 'true' && filters.incomplete === 'true') {
        progressWhere[Op.or] = [
          { [Op.and]: completeSections.map(s => ({ [s]: { [Op.ne]: 'not done' } })) },
          { [Op.or]: completeSections.map(s => ({ [s]: { [Op.ne]: 'done' } })) }
        ];
      } else if (filters.complete === 'true') {
        progressWhere[Op.and] = completeSections.map(s => ({ [s]: { [Op.ne]: 'not done' } }));
      } else if (filters.incomplete === 'true') {
        progressWhere[Op.or] = completeSections.map(s => ({ [s]: { [Op.ne]: 'done' } }));
      }
    }

    // Status filter
    if (filters.status === 'active') {
      where.status = 'active';
    } else if (filters.status === 'inactive') {
      where.status = { [Op.like]: '%inactive%' };
    }

    // Sorting
    const order = [[filters.sort_by || 'created_date', (filters.sort_order || 'DESC').toUpperCase()]];

    // Query
    const { count, rows: patients } = await db.Patient.findAndCountAll({
      where,
      include: [
        {
          model: db.RegisterCriteria,
          as: 'register_criteria',
          required: false,
          where: Object.keys(criteriaWhere).length ? criteriaWhere : undefined,
        },
        {
          model: db.SectionProgress,
          as: 'section_progress',
          required: false,
          where: Object.keys(progressWhere).length ? progressWhere : undefined,
        },
      ],
      order,
      // raw: true,
      // nest: true,
      limit,
      offset
    });

    // Add refer status (latest per TNR)
    const tnrs = patients.map(p => p.TNR);
    let referStatusMap = {};
    if (tnrs.length) {
      const referStatuses = await db.sequelize.query(
        `SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = ? AND TNR IN (?) ORDER BY refer_id DESC) AS referx GROUP BY TNR`,
        {
          replacements: [hospital, tnrs],
          type: Sequelize.QueryTypes.SELECT,
        }
      );
      referStatuses.forEach(row => {
        referStatusMap[row.TNR] = row.status;
      });
    }

    // Attach refer_status
    const result = patients.map(p => ({
      ...p,
      refer_status: referStatusMap[p.TNR] || null
    }));
    return { count, rows: result };
  } catch (error) {
    logger.error('Error filtering patients:', error);
    throw error;
  }
}

/**
 * Fetches a patient by TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<Object|null>} Patient record or null
 */
async function getPatientByTnrHospital(TNR, hospital) {
  return await db.Patient.findOne({ where: { TNR, hospital } });
}

/**
 * Fetches all section_progress records by TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<Array>} Array of section_progress records
 */
async function getSectionProgressByTnrHospital(TNR, hospital) {
  return await db.SectionProgress.findAll({ where: { TNR, hospital } });
}

/**
 * Fetches all register_progress records by TNR.
 * @param {string} TNR
 * @returns {Promise<Array>} Array of register_progress records
 */
async function getRegisterProgressByTnr(TNR) {
  return await db.RegisterProgress.findAll({ where: { TNR } });
}

/**
 * Counts refer records by TNR and hospital with status 'waiting' or 'non-mem refer'.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Count of matching refer records
 */
async function countReferByTnrAndHospital(TNR, hospital) {
  return await db.Refer.count({
    where: {
      TNR,
      hospital,
      status: { [db.Sequelize.Op.or]: ['waiting', 'non-mem refer'] }
    }
  });
}

/**
 * Updates refer status to 'cancel' for a given TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Number of affected rows
 */
async function cancelReferByTnrAndHospital(TNR, hospital) {
  const [affectedRows] = await db.Refer.update(
    { status: 'cancel' },
    { where: { TNR, hospital } }
  );
  return affectedRows;
}

/**
 * Updates refer status from 'reject' to 'reject2' for a given TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Number of affected rows
 */
async function updateReferStatusToReject2(TNR, hospital) {
  const [affectedRows] = await db.Refer.update(
    { status: 'reject2' },
    { where: { TNR, hospital, status: 'reject' } }
  );
  return affectedRows;
}

/**
 * Updates patient status to 'inactive' for a given TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Number of affected rows
 */
async function setPatientStatusInactive(TNR, hospital) {
  const [affectedRows] = await db.Patient.update(
    { status: 'inactive' },
    { where: { TNR, hospital } }
  );
  return affectedRows;
}

/**
 * Generates and inserts a TNR into patient_concurrence table with retry logic
 * Mimics the PHP logic from the original code
 * @param {string} registDate - Registration date in format YYYY-MM-DD
 * @param {string} hospital - Hospital identifier
 * @returns {Promise<string|number>} Generated TNR on success, 0 on failure
 */
async function generateAndInsertTNR(registDate, hospital) {
  const maxRetries = 3;

  for (let i = 0; i < maxRetries; i++) {
    try {
      // Generate a new TNR (now async)
      const tnr = await generateTNR(registDate, hospital);

      // Try to insert into patient_concurrence
      await db.PatientConcurrence.create({ TNR: tnr });

      logger.info(`Successfully generated and inserted TNR: ${tnr} for hospital: ${hospital}`);
      return tnr;
    } catch (error) {
      logger.warn(`Attempt ${i + 1} failed to insert TNR for hospital ${hospital}:`, error.message);

      // If this is the last attempt, log the final failure
      if (i === maxRetries - 1) {
        logger.error(`Failed to generate TNR after ${maxRetries} attempts for hospital ${hospital}:`, error);
        return 0;
      }

      // Wait 1 second before retry (mimicking PHP sleep(1))
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return 0;
}

/**
 * Creates a new patient record with section progress
 * Accepts patient data as JSON object
 * @param {string} TNR - The TNR for the patient
 * @param {Object} patientData - Patient data as JSON object
 * @param {string} hospital - Hospital identifier
 * @returns {Promise<string>} Success message
 */
async function createPatientWithProgress(TNR, patientData, hospital) {
  try {
    // Add created_date like PHP: $created_date = date("Y-m-d H:i:s");
    const createdDate = new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Define default values for ALL required fields (all fields have allowNull: false)
    const defaultValues = {
      // Primary fields
      fullname: '',
      HN: '',

      // Identity fields
      infant_id: '',
      passport_id: '',
      illegal: 'No',
      illegal_id: '',
      illegal_hospital: '',

      // Personal info
      DOB: '', // Default date
      sex: '',    // Default sex
      ethnic: '',

      // Mother info
      mother_fullname: '',
      mother_id: '',
      mother_passport: '',
      mother_address: '',
      mother_tel: '',

      // Contact info
      contact_person_name: '',
      relation: '',
      contact_person_tel: '',
      other_contact: '',

      // Status
      status: 'active'
    };

    // Merge user data with defaults, user data takes precedence
    const finalPatientData = {
      ...defaultValues,     // Default values first
      ...patientData,       // User provided data overrides defaults
      TNR: TNR,            // Ensure TNR is included
      created_date: createdDate,
      hospital: hospital    // Set hospital directly
    };

    // Insert into patient table using generic function
    await addToDatabase('patient', finalPatientData);

    // Insert into section_progress table (like PHP INSERT query)
    await db.SectionProgress.create({
      TNR: TNR,
      hospital: hospital,
      time: createdDate
    });

    logger.info(`Successfully created patient with TNR: ${TNR} for hospital: ${hospital}`);

    return {
      status: 1,
      message: "Patient created successfully",
      data: {
        TNR: TNR,
        hospital: hospital,
        created_date: createdDate
      }
    };

  } catch (error) {
    logger.error(`Error creating patient with TNR ${TNR}:`, error);
    throw error;
  }
}

/**
 * Validates patient existence by infant_id and handles patient_concurrence cleanup
 * Equivalent to the PHP validation logic
 * @param {string} infantId - The infant ID to check
 * @param {string} TNR - The TNR for cleanup if patient exists
 * @returns {Promise<{status: string, message: string}>}
 */
async function validatePatientByInfantId(infantId, TNR) {
  try {
    // Check if patient exists with the given infant_id
    const existingPatient = await db.Patient.findOne({
      where: { infant_id: infantId }
    });

    if (!existingPatient) {
      // No patient found - validation passes
      logger.info(`Patient validation passed - no patient found with infant_id: ${infantId}`);
      return {
        status: "pass",
        message: "Patient validation successful - no existing patient found"
      };
    } else {
      // Patient exists - clean up patient_concurrence and return error
      await db.PatientConcurrence.destroy({
        where: { TNR: TNR }
      });

      logger.warn(`Patient validation failed - patient exists with infant_id: ${infantId}, cleaned up patient_concurrence for TNR: ${TNR}`);
      return {
        status: "error",
        message: "Patient with this infant ID already exists"
      };
    }

  } catch (error) {
    logger.error(`Error validating patient with infant_id ${infantId}:`, error);
    throw error;
  }
}

/**
 * Creates or updates register progress record
 * Equivalent to the PHP register progress logic
 * @param {string} TNR - The TNR
 * @param {string} hospital - The hospital (from session/auth)
 * @param {string} temporary - Temporary ID value
 * @param {string} permanent - Permanent ID value
 * @returns {Promise<{status: number, message: string, data: object}>}
 */
async function createRegisterProgress(TNR, hospital, temporary, permanent) {
  try {
    // Determine status based on permanent value (like PHP logic)
    const status = permanent && permanent.trim() !== "" ? "done" : "not done";

    // Prepare data array like PHP (TNR, hospital, temporary, permanent, status)
    const registerProgressData = {
      TNR: TNR,
      hospital: hospital,
      ID_Temporary: temporary || "",
      ID_Permanent: permanent || "",
      status: status
    };

    // Use the generic addToDatabase function (like PHP add_to_database)
    const result = await addToDatabase('register_progress', registerProgressData);

    logger.info(`Successfully created register progress for TNR: ${TNR}, hospital: ${hospital}`);

    return {
      status: 1,
      message: "Register progress created successfully",
      data: {
        TNR: TNR,
        hospital: hospital,
        temporary: temporary || "",
        permanent: permanent || "",
        status: status,
        response: `${temporary || ""}  ${permanent || ""}` // PHP-like response format
      }
    };

  } catch (error) {
    logger.error(`Error creating register progress for TNR ${TNR}:`, error);
    throw error;
  }
}

/**
 * Get patient sections data for export (similar to PHP functionality)
 * @param {string} TNR - Patient TNR
 * @param {string} hospital - Hospital name
 * @param {Array} sections - Sections to include
 * @returns {Promise<Object>} Patient sections data
 */
async function getPatientSectionsData(TNR, hospital, sections = ['section1', 'section2', 'section3', 'section4']) {
  try {
    const whereClause = { TNR, hospital };

    const result = {
      patient: null,
      sections: {}
    };

    // Get patient basic info
    const patient = await db.Patient.findOne({ where: whereClause });
    result.patient = patient ? patient.toJSON() : null;

    // Debug logging
    logger.info(`Patient data for TNR ${TNR}:`, {
      found: !!patient,
      patientData: result.patient ? 'Patient data exists' : 'No patient data'
    });

    // Get sections data based on requested sections
    for (const sectionName of sections) {
      try {
        result.sections[sectionName] = await getSectionData(sectionName, TNR, hospital);
      } catch (error) {
        logger.warn(`Failed to fetch ${sectionName} for TNR ${TNR}:`, error.message);
        result.sections[sectionName] = null;
      }
    }

    return result;

  } catch (error) {
    logger.error(`Error getting patient sections data for TNR ${TNR}:`, error);
    throw error;
  }
}

/**
 * Get specific section data (similar to PHP section queries)
 * @param {string} sectionName - Section name
 * @param {string} TNR - TNR
 * @param {string} hospital - Hospital
 * @returns {Promise<Object>} Section data
 */
async function getSectionData(sectionName, TNR, hospital) {
  const whereClause = { TNR, hospital };

  switch (sectionName.toLowerCase()) {
    case 'section1':
      const section1 = await db.Section1.findOne({ where: whereClause });
      return section1 ? section1.toJSON() : null;

    case 'section2':
      // Section2 has multiple related tables (like PHP queries)
      const section2 = await db.Section2.findOne({ where: whereClause });
      const section2Data = section2 ? section2.toJSON() : null;

      if (section2Data) {
        // Get related section2 data (similar to PHP multiple queries)
        section2Data.abnormal_serology = await getSection2Related('Section2AbnormalSerology', TNR, hospital);
        section2Data.complications = await getSection2Related('Section2ComplicationDuringPregnancy', TNR, hospital);
        section2Data.intrapartum = await getSection2Related('Section2IntrapartumComplication', TNR, hospital);
        section2Data.gbs = await getSection2Related('Section2Gbs', TNR, hospital);
        section2Data.medication = await getSection2Related('Section2MeternalMedication', TNR, hospital);
      }

      return section2Data;

    case 'section3':
      const section3 = await db.Section3.findOne({ where: whereClause });
      return section3 ? section3.toJSON() : null;

    case 'section4':
      const section4 = await db.Section4.findOne({ where: whereClause });
      return section4 ? section4.toJSON() : null;

    case 'section5':
      const section5 = await db.Section5.findOne({ where: whereClause });
      return section5 ? section5.toJSON() : null;

    case 'section6':
      const section6 = await db.Section6.findOne({ where: whereClause });
      return section6 ? section6.toJSON() : null;

    default:
      return null;
  }
}

/**
 * Get section2 related data (similar to PHP section2 related queries)
 * @param {string} modelName - Model name
 * @param {string} TNR - TNR
 * @param {string} hospital - Hospital
 * @returns {Promise<Object>} Related data
 */
async function getSection2Related(modelName, TNR, hospital) {
  try {
    const whereClause = { TNR, hospital };

    const model = db[modelName];
    if (!model) return null;

    const data = await model.findOne({ where: whereClause });
    return data ? data.toJSON() : null;
  } catch (error) {
    logger.warn(`Failed to fetch ${modelName}:`, error.message);
    return null;
  }
}

module.exports = {
  removeUserConcurrence,
  fetchAllPatients,
  filterPatientsByCriteria,
  getPatientByTnrHospital,
  getSectionProgressByTnrHospital,
  getRegisterProgressByTnr,
  countReferByTnrAndHospital,
  cancelReferByTnrAndHospital,
  updateReferStatusToReject2,
  setPatientStatusInactive,
  generateAndInsertTNR,
  createPatientWithProgress,
  validatePatientByInfantId,
  createRegisterProgress,
  getPatientSectionsData
};