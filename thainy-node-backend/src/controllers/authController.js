// src/controllers/authController.js

const { loginUser, handleForgotPassword, checkOrChangePassword } = require('../services/authService');
const jwtUtils = require('../utils/jwt');
const logger = require('../utils/logger');

async function login(req, res) {
  try {
    const { username, password } = req.body;
    if (!username || !password) {
      return res.status(400).json({ status: 0, message: "Username and password are required." });
    }
    const result = await loginUser(username, password);

    if (result.status === 0) {
      return res.status(401).json({
        status: 0,
        message: result.message
      });
    }

    const payload = {
      username: result.username,
      email: result.email,
      hospital: result.hospital,
    };
    const accessToken = jwtUtils.generateAccessToken(payload);
    const refreshToken = jwtUtils.generateRefreshToken(payload);

    return res.status(200).json({
      status: 1,
      pin: result.pin,
      username: result.username,
      email: result.email,
      hospital: result.hospital,
      accessToken,
      refreshToken,
      expiresIn: jwtUtils.ACCESS_TOKEN_EXPIRES_IN || '15m',
      message: result.message
    });
  } catch (error) {
    logger.error('Login failed:', error);
    return res.status(500).json({ status: 0, message: 'Internal server error' });
  }
}

function refreshAccessToken(req, res) {
  const { refreshToken } = req.body;
  if (!refreshToken) {
    return res.status(400).json({ message: 'Refresh token is required.' });
  }
  try {
    const payload = jwtUtils.verifyRefreshToken(refreshToken);
   
    const { iat, exp, ...newPayload } = payload;
    const accessToken = jwtUtils.generateAccessToken(newPayload);
    
    return res.status(200).json({
      accessToken,
      expiresIn: jwtUtils.ACCESS_TOKEN_EXPIRES_IN || '15m',
      message: 'Access token refreshed successfully.'
    });
  } catch (error) {
    logger.error('Invalid or expired refresh token:', error);
    return res.status(403).json({ message: 'Invalid or expired refresh token.' });
  }
}

function logout(req, res) {
  return res.status(501).json({ message: 'Logout functionality not implemented yet.' });
}

async function forgotPassword(req, res) {
  try {
    const { email } = req.body;
    if (!email || typeof email !== 'string' || email.trim() === '') {
      return res.status(400).json({ status: 0, message: 'Email is required.' });
    }
    const result = await handleForgotPassword(email);
    if (result.status === 0) {
      return res.status(404).json({ status: 0, message: 'No account found with that email address.' });
    }
    // For now, just return success (email sending is logged)
    return res.status(200).json({ status: 1, message: 'Password reset link sent (check logs for link)' });
  } catch (error) {
    logger.error('Forgot password failed:', error);
    return res.status(500).json({ status: 0, message: 'Internal server error.' });
  }
}

async function checkPassword(req, res) {
  try {
    const { username, password } = req.body;
    if (!username || !password) {
      return res.status(400).json({ status: 0, message: 'Username and password are required.' });
    }
    const result = await checkOrChangePassword({ username, password, action: 'check' });
    if (result.status === 0) {
      return res.status(result.code || 400).json({ status: 0, message: result.message });
    }
    return res.status(200).json({ status: 1, message: result.message });
  } catch (error) {
    logger.error('Password check failed:', error);
    return res.status(500).json({ status: 0, message: 'Internal server error.' });
  }
}

async function changePassword(req, res) {
  try {
    const { password } = req.body;
    if (!password) {
      return res.status(400).json({ status: 0, message: 'Password is required.' });
    }
    if (!req.user || !req.user.username) {
      return res.status(401).json({ status: 0, message: 'Authentication required to change password.' });
    }
    const username = req.user.username;
    const result = await checkOrChangePassword({ username, password, action: 'change' });
    if (result.status === 0) {
      return res.status(result.code || 400).json({ status: 0, message: result.message });
    }
    return res.status(200).json({ status: 1, message: result.message });
  } catch (error) {
    logger.error('Password change failed:', error);
    return res.status(500).json({ status: 0, message: 'Internal server error.' });
  }
}

module.exports = {
  login,
  refreshAccessToken,
  logout,
  forgotPassword,
  checkPassword,
  changePassword,
};
