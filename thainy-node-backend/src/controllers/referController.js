const { getReferredPatients } = require('../services/referService');
const logger = require('../utils/logger');

async function listReferredPatients(req, res) {
  try {
    const destination_hospital = req.user?.hospital || req.query.destination_hospital;
    const status = req.query.status || 'waiting';
    if (!destination_hospital) {
      return res.status(400).json({ message: 'Destination hospital is required.' });
    }
    const patients = await getReferredPatients({ destination_hospital, status });
    res.json(patients);
  } catch (error) {
    logger.error('Error fetching referred patients:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

module.exports = { listReferredPatients }; 