const { getUserData } = require('../services/userService');
const logger = require('../utils/logger');
const { handleUserConcurrence } = require('../services/userService');

async function getUser(req, res) {
  try {
    const { username, email, hospital } = req.user;
    const user = await getUserData({ username, email, hospital });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.json(user);
  } catch (error) {
    logger.error(`Error fetching user data for username ${req.user?.username}, email ${req.user?.email}, hospital ${req.user?.hospital}:`, error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /user/concurrence
 * Body: { TNR }
 * Checks and sets user concurrence for a TNR, returns status and criteria.
 */
async function userConcurrence(req, res) {
  try {
    const username = req.user?.username || req.body.username; // fallback for testing
    const { TNR } = req.body;
    if (!TNR || !username) {
      return res.status(400).json({ status: 0, message: 'TNR and username are required.' });
    }
    // Check if TNR exists in register_criteria
    const db = require('../models');
    const criteriaRow = await db.RegisterCriteria.findOne({ where: { TNR } });
    if (!criteriaRow) {
      return res.status(404).json({ status: 0, message: 'Invalid TNR: no such record in register_criteria.' });
    }
    const result = await handleUserConcurrence(username, TNR);
    res.json(result);
  } catch (error) {
    console.error('Error in userConcurrence:', error);
    res.status(500).json({ status: 0, message: 'Internal server error' });
  }
}

module.exports = { getUser, userConcurrence };
