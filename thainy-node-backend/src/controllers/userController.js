const { getUserData, handleUserConcurrence, createOrUpdatePin } = require('../services/userService');
const logger = require('../utils/logger');

async function getUser(req, res) {
  try {
    const { username, email, hospital } = req.user;
    const user = await getUserData({ username, email, hospital });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.json(user);
  } catch (error) {
    logger.error(`Error fetching user data for username ${req.user?.username}, email ${req.user?.email}, hospital ${req.user?.hospital}:`, error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /user/concurrence
 * Body: { TNR }
 * Checks and sets user concurrence for a TNR, returns status and criteria.
 */
async function userConcurrence(req, res) {
  try {
    const username = req.user?.username || req.body.username; // fallback for testing
    const { TNR } = req.body;
    if (!TNR || !username) {
      return res.status(400).json({ status: 0, message: 'TNR and username are required.' });
    }
    // Check if TNR exists in register_criteria
    const db = require('../models');
    const criteriaRow = await db.RegisterCriteria.findOne({ where: { TNR } });
    if (!criteriaRow) {
      return res.status(404).json({ status: 0, message: 'Invalid TNR: no such record in register_criteria.' });
    }
    const result = await handleUserConcurrence(username, TNR);
    res.json(result);
  } catch (error) {
    console.error('Error in userConcurrence:', error);
    res.status(500).json({ status: 0, message: 'Internal server error' });
  }
}

/**
 * POST /user/pin
 * Creates or updates a PIN for the authenticated user
 * Equivalent to the PHP PIN management logic
 */
async function savePin(req, res) {
  try {
    const { pin } = req.body;
    const username = req.user?.username; // Get username from authenticated session

    // Validate required parameters
    if (!pin) {
      return res.status(400).json({
        status: 0,
        message: 'PIN is required'
      });
    }

    if (!username) {
      return res.status(400).json({
        status: 0,
        message: 'Username is required. Please ensure you are authenticated.'
      });
    }

    // Validate PIN format (6 digits based on model)
    if (!/^\d{1,6}$/.test(pin)) {
      return res.status(400).json({
        status: 0,
        message: 'PIN must be numeric and up to 6 digits'
      });
    }

    // Call the service function
    const result = await createOrUpdatePin(username, pin);

    // Return success response (matching PHP response format)
    return res.status(200).json({
      status: result.status,
      message: result.message,
      data: result.data
    });

  } catch (error) {
    logger.error('Error creating/updating PIN:', error);

    // Handle any database constraint errors
    if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeConstraintError') {
      return res.status(400).json({
        status: 0,
        message: 'Invalid PIN data provided'
      });
    }

    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

module.exports = { getUser, userConcurrence, savePin };
