const { createRegisterCriteria } = require('../services/registerCriteriaService');
const logger = require('../utils/logger');

/**
 * POST /register-criteria/create
 * Body: { isdata: "JSON_STRING" } OR direct JSON object - Supports both formats
 * Creates a new register criteria record
 */
async function createRegisterCriteriaHandler(req, res) {
  try {
    let criteriaData;

    // Support both formats:
    // 1. Legacy PHP format: { isdata: "JSON_STRING" }
    // 2. Direct JSON object: { TNR: "...", ward: "...", ... }

    if (req.body.isdata) {
      // Legacy format - parse JSON string (like PHP json_decode)
      try {
        criteriaData = JSON.parse(req.body.isdata);
      } catch (parseError) {
        return res.status(400).json({
          status: 0,
          message: 'Invalid JSON format in isdata field.'
        });
      }
    } else if (req.body.TNR || req.body.ward || req.body.pna || req.body.patient_symptoms) {
      // Direct JSON object format
      criteriaData = req.body;
    } else {
      return res.status(400).json({
        status: 0,
        message: 'Either isdata field (JSON string) or direct JSON object with TNR, ward, pna, patient_symptoms is required.'
      });
    }

    // Validate required fields for register_criteria
    if (!criteriaData.TNR || !criteriaData.ward || !criteriaData.pna || !criteriaData.patient_symptoms) {
      return res.status(400).json({
        status: 0,
        message: 'TNR, ward, pna, and patient_symptoms are required fields.'
      });
    }

    // Create the register criteria record
    await createRegisterCriteria(criteriaData);

    // Return the data like PHP (echo $mydata)
    res.json(criteriaData);

  } catch (error) {
    logger.error('Error creating register criteria:', error);

    // Handle duplicate key error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        status: 0,
        message: 'Register criteria with this TNR already exists.'
      });
    }

    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

module.exports = {
  createRegisterCriteriaHandler
};
