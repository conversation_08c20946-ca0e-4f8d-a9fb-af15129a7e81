const {
  removeUserConcurrence,
  fetchAllPatients,
  filterPatientsByCriteria,
  getPatientByTnrHospital,
  getSectionProgressByTnrHospital,
  getRegisterProgressByTnr,
  countReferByTnrAndHospital,
  cancelReferByTnrAndHospital,
  updateReferStatusToReject2,
  setPatientStatusInactive,
  generateAndInsertTNR,
  createPatientWithProgress,
  validatePatientByInfantId,
  createRegisterProgress
} = require('../services/patientService');
const logger = require('../utils/logger');
const { getPagination, getPagingData } = require('../utils/pagination');

async function getAllPatients(req, res) {
  try {
    const { hospital, username } = req.user;
    const { status } = req.query;
    if (!status) {
      return res.status(400).json({
        status: 0,
        message: 'Status is required as a query parameter.'
      });
    }
    await removeUserConcurrence(username);
    const { limit, offset } = getPagination(req.query, 10, 100);
    const data = await fetchAllPatients({ hospital, status, limit, offset });
    const page = req.query.page ? parseInt(req.query.page, 10) : Math.floor(offset / limit) + 1;
    const response = getPagingData(data, page, limit);
    res.json(response);
  } catch (error) {
    logger.error('Error fetching patients:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

async function searchPatients(req, res) {
  try {
    const { hospital } = req.user;
    const filters = req.body;
    const { limit, offset } = getPagination(req.query, 10, 100);
    const data = await filterPatientsByCriteria(hospital, filters, limit, offset);
    const page = req.query.page ? parseInt(req.query.page, 10) : Math.floor(offset / limit) + 1;
    const response = getPagingData(data, page, limit);
    res.json(response);
  } catch (error) {
    logger.error('Error filtering patients:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patient/by-tnr-hospital
 * Body: { TNR, hospital }
 * Returns the patient record for the given TNR and hospital.
 */
async function getPatientByTnrAndHospital(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({
        status: 0,
        message: 'TNR and hospital are required.'
      });
    }
    const patient = await getPatientByTnrHospital(TNR, hospital);
    if (!patient) {
      return res.status(404).json({
        status: 0,
        message: 'No patient found for the given TNR and hospital.'
      });
    }
    res.json({
      status: 1,
      data: patient
    });
  } catch (error) {
    logger.error('Error fetching patient by TNR and hospital:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patient/section-progress
 * Body: { TNR, hospital }
 * Returns all section_progress records for the given TNR and hospital.
 */
async function getSectionProgress(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({
        status: 0,
        message: 'TNR and hospital are required.'
      });
    }
    const progress = await getSectionProgressByTnrHospital(TNR, hospital);
    if (!progress || progress.length === 0) {
      return res.status(404).json({
        status: 0,
        message: 'No section progress found for the given TNR and hospital.'
      });
    }
    res.json({
      status: 1,
      data: progress
    });
  } catch (error) {
    logger.error('Error fetching section_progress by TNR and hospital:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/register-progress
 * Body: { TNR }
 * Returns all register_progress records for the given TNR.
 */
async function getRegisterProgress(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    if (!TNR) {
      return res.status(400).json({
        status: 0,
        message: 'TNR is required.'
      });
    }
    const progress = await getRegisterProgressByTnr(TNR);
    if (!progress || progress.length === 0) {
      return res.status(404).json({
        status: 0,
        message: 'No register progress found for the given TNR.'
      });
    }
    res.json({
      status: 1,
      data: progress
    });
  } catch (error) {
    logger.error('Error fetching register_progress by TNR:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/refer-count
 * Body: { TNR, hospital }
 * Returns the count of refer records for the given TNR and hospital with status 'waiting' or 'non-mem refer'.
 */
async function getReferCount(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({
        status: 0,
        message: 'TNR and hospital are required.'
      });
    }
    const count = await countReferByTnrAndHospital(TNR, hospital);
    res.json({
      status: 1,
      data: { count }
    });
  } catch (error) {
    logger.error('Error counting refer records by TNR and hospital:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/cancel-refer
 * Body: { TNR, hospital }
 * Updates refer status to 'cancel' for the given TNR and hospital.
 */
async function cancelRefer(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({
        status: 0,
        message: 'TNR and hospital are required.'
      });
    }
    const affectedRows = await cancelReferByTnrAndHospital(TNR, hospital);
    if (affectedRows === 0) {
      return res.status(404).json({
        status: 0,
        message: 'No refer records found to update for the given TNR and hospital.'
      });
    }
    res.json({
      status: 1,
      message: 'Refer status updated to cancel.',
      data: { affectedRows }
    });
  } catch (error) {
    logger.error('Error updating refer status to cancel:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/reject2-refer
 * Body: { TNR, hospital }
 * Updates refer status from 'reject' to 'reject2' for the given TNR and hospital.
 */
async function updateReferToReject2(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({
        status: 0,
        message: 'TNR and hospital are required.'
      });
    }
    const affectedRows = await updateReferStatusToReject2(TNR, hospital);
    if (affectedRows === 0) {
      return res.status(404).json({
        status: 0,
        message: 'No refer records with status reject found to update for the given TNR and hospital.'
      });
    }
    res.json({
      status: 1,
      message: 'Refer status updated to reject2.',
      data: { affectedRows }
    });
  } catch (error) {
    logger.error('Error updating refer status to reject2:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/set-inactive
 * Body: { TNR, hospital }
 * Sets patient status to 'inactive' for the given TNR and hospital.
 */
async function setPatientInactive(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({
        status: 0,
        message: 'TNR and hospital are required.'
      });
    }
    const affectedRows = await setPatientStatusInactive(TNR, hospital);
    if (affectedRows === 0) {
      return res.status(404).json({
        status: 0,
        message: 'No patient found to update for the given TNR and hospital.'
      });
    }
    res.json({
      status: 1,
      message: 'Patient status updated to inactive.',
      data: { affectedRows }
    });
  } catch (error) {
    logger.error('Error updating patient status to inactive:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/generate-tnr
 * Body: { regist_date }
 * Generates a TNR and inserts it into patient_concurrence table
 * Uses hospital from authenticated user session
 */
async function generateTNR(req, res) {
  try {
    const { regist_date } = req.body;
    const hospital = req.user?.hospital;

    // Validate required fields
    if (!regist_date) {
      return res.status(400).json({
        status: 0,
        message: 'Registration date (regist_date) is required.'
      });
    }

    if (!hospital) {
      return res.status(400).json({
        status: 0,
        message: 'Hospital information is required. Please ensure you are authenticated.'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(regist_date)) {
      return res.status(400).json({
        status: 0,
        message: 'Invalid date format. Please use YYYY-MM-DD format.'
      });
    }

    // Generate and insert TNR
    const result = await generateAndInsertTNR(regist_date, hospital);

    if (result === 0) {
      return res.json(0);
    }

    // Return success response with generated TNR (matching PHP response format)
    res.json(result);

  } catch (error) {
    logger.error('Error generating TNR:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/create
 * Body: JSON object with patient data including TNR
 * Creates a new patient with section progress
 */
async function createPatient(req, res) {
  try {
    const { TNR, ...patientData } = req.body;
    const hospital = req.user?.hospital;

    // Validate required fields
    if (!TNR) {
      return res.status(400).json({
        status: 0,
        message: 'TNR is required.'
      });
    }

    if (!hospital) {
      return res.status(400).json({
        status: 0,
        message: 'Hospital information is required. Please ensure you are authenticated.'
      });
    }

    // Validate that we have patient data
    if (!patientData || Object.keys(patientData).length === 0) {
      return res.status(400).json({
        status: 0,
        message: 'Patient data is required.'
      });
    }

    // Validate critical fields (recommended for data quality)
    if (!patientData.fullname) {
      return res.status(400).json({
        status: 0,
        message: 'Patient fullname is required.'
      });
    }

    if (!patientData.HN) {
      return res.status(400).json({
        status: 0,
        message: 'Patient HN (Hospital Number) is required.'
      });
    }

    // Create patient with progress
    const result = await createPatientWithProgress(TNR, patientData, hospital);

    // Return "ok" like PHP
    res.json(result);

  } catch (error) {
    logger.error('Error creating patient:', error);

    // Handle duplicate key error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        status: 0,
        message: 'Patient with this TNR already exists.'
      });
    }

    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/validate-infant-id
 * Validates if a patient with given infant_id already exists
 * Equivalent to the PHP validation logic
 */
async function validateInfantId(req, res) {
  try {
    const { id: infantId, TNR } = req.body;

    // Validate required parameters
    if (!infantId || !TNR) {
      return res.status(400).json({
        status: 0,
        message: 'Both id (infant_id) and TNR are required'
      });
    }

    // Call the service function
    const result = await validatePatientByInfantId(infantId, TNR);

    // Return response based on PHP logic
    if (result.status === "pass") {
      return res.status(200).json({
        status: 1,
        message: result.message,
        result: "pass"
      });
    } else {
      return res.status(409).json({
        status: 0,
        message: result.message,
        result: "error"
      });
    }

  } catch (error) {
    logger.error('Error validating patient by infant ID:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/create-register-progress
 * Creates or updates register progress record
 * Equivalent to the PHP register progress logic
 */
async function saveRegisterProgress(req, res) {
  try {
    const { TNR, temporary, permanent } = req.body;
    const hospital = req.user?.hospital; // Get hospital from authenticated user session

    // Validate required parameters
    if (!TNR) {
      return res.status(400).json({
        status: 0,
        message: 'TNR is required'
      });
    }

    if (!hospital) {
      return res.status(400).json({
        status: 0,
        message: 'Hospital information is required. Please ensure you are authenticated.'
      });
    }

    // Call the service function
    const result = await createRegisterProgress(TNR, hospital, temporary, permanent);

    // Return success response (matching PHP response format)
    return res.status(200).json({
      status: result.status,
      message: result.message,
      data: result.data,
      // PHP-like simple response format
      response: result.data.response
    });

  } catch (error) {
    logger.error('Error creating register progress:', error);

    // Handle duplicate key error (if TNR already exists)
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        status: 0,
        message: 'Register progress for this TNR already exists.'
      });
    }

    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

module.exports = {
  getAllPatients,
  searchPatients,
  getPatientByTnrAndHospital,
  getSectionProgress,
  getRegisterProgress,
  getReferCount,
  cancelRefer,
  updateReferToReject2,
  setPatientInactive,
  generateTNR,
  createPatient,
  validateInfantId,
  saveRegisterProgress
};