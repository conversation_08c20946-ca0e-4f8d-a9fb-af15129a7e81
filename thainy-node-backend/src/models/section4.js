const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section4', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    DOB: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    TOB: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    gender: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    gestational_age_week: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    gestational_age_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    birth_weight: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    length: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    length_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    head_circumference: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    head_circumference_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    growth_status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section4',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
