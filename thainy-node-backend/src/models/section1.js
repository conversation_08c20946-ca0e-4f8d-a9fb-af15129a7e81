const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section1', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    admission_status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    intrauterine_transter: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    transfer_member: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    hospital_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    non_hospital_name: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    admitted_to: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    ward: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other_ward: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    admission_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_type: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    stay_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_hospital: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_hospital_code: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    discharge_level2_hospital: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    discharge_non_mem_hospital: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    discharge_at_age: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    discharge_hospital_day: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section1',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
