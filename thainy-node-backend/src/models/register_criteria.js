const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  const RegisterCriteria = sequelize.define('RegisterCriteria', {
    TNR: {
      type: DataTypes.STRING(15),
      allowNull: false,
      primaryKey: true
    },
    ward: {
      type: DataTypes.STRING(5),
      allowNull: false
    },
    pna: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    patient_symptoms: {
      type: DataTypes.STRING(300),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'register_criteria',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
        ]
      },
      {
        name: "TNR",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
        ]
      },
    ]
  });

  RegisterCriteria.associate = function(models) {
    RegisterCriteria.belongsTo(models.Patient, { foreignKey: 'TNR', targetKey: 'TNR', as: 'patient' });
  };

  return RegisterCriteria;
};
