const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section2MeternalMedication', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    antibiotics: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    time: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    dexamamthasone_or_prenatal_steroid: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    course: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    MgSo4: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    no_complication: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section2_meternal_medication',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
