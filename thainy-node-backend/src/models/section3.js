const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section3', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    delivery_mode: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    vaginal: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    elective: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    emergency: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cephalopelvic: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    caesarean_other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    caesarean_other_text: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '1_min_score': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '1_min_NA': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '5_min_score': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '5_min_NA': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '10_min_score': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '10_min_NA': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    resuscitation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    CPAP: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PPV: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    intubation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    chest_compression: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    epinephrine: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cord_blood: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cord_blood_pH: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cord_blood_pCO2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cord_blood_HCO2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cord_blood_BE: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    delayed_cord_clamping: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section3',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
