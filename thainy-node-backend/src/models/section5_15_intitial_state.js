const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section515IntitialState', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    intitial: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    reason: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_15_intitial_state',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "T<PERSON>" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
