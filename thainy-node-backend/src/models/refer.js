const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  const Refer = sequelize.define('Refer', {
    refer_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    destination_hospital: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    refer_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    text: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    referer_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    response_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    response_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'refer',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "refer_id" },
        ]
      },
    ]
  });

  Refer.associate = function(models) {
    Refer.belongsTo(models.Patient, {
      foreignKey: 'TNR',
      targetKey: 'TNR',
      as: 'patient',
      constraints: false
    });
  };

  return Refer;
};
