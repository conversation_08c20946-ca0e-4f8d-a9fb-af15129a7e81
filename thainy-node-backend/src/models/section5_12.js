const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section512', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    severity_within_6_hr: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    severity_the_worst: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    receive_TH: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cooling_start_at_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cooling_start_at_time: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    after_birth: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "-"
    },
    temperature: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    complete_cooling: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    specify: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    duration: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_phenobarbital: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_phenytoin: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_midazolam: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_levetiracetam: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_topiramate: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_diazepam: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_other_main: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    seizure_none: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    start_anticonvulsant: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    aEEG_or_EGG_done: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    normal_background: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    abnormal: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    aEEG_seizure: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    result_other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_12',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
