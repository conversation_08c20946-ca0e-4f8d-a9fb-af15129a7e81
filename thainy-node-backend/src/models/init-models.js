var DataTypes = require("sequelize").DataTypes;
var _Noti15D = require("./Noti_15D");
var _Noti1Y = require("./Noti_1Y");
var _Noti30D = require("./Noti_30D");
var _Admin = require("./admin");
var _ChatHistory = require("./chat_history");
var _ForgetPasswordToken = require("./forget_password_token");
var _Hospital = require("./hospital");
var _Patient = require("./patient");
var _PatientConcurrence = require("./patient_concurrence");
var _Pin = require("./pin");
var _Refer = require("./refer");
var _RegisterCriteria = require("./register_criteria");
var _RegisterProgress = require("./register_progress");
var _RegisterToken = require("./register_token");
var _Section1 = require("./section1");
var _Section2 = require("./section2");
var _Section2Gbs = require("./section2_GBS");
var _Section2AbnormalSerology = require("./section2_abnormal_serology");
var _Section2ComplicationDuringPregnancy = require("./section2_complication_during_pregnancy");
var _Section2IntrapartumComplication = require("./section2_intrapartum_complication");
var _Section2MeternalMedication = require("./section2_meternal_medication");
var _Section2Other = require("./section2_other");
var _Section3 = require("./section3");
var _Section4 = require("./section4");
var _Section51 = require("./section5_1");
var _Section510 = require("./section5_10");
var _Section510PiccLine = require("./section5_10_PICC_line");
var _Section510Uac = require("./section5_10_UAC");
var _Section510Uvc = require("./section5_10_UVC");
var _Section510Day = require("./section5_10_day");
var _Section510OtherCentralLine = require("./section5_10_other_central_line");
var _Section511 = require("./section5_11");
var _Section511Detail = require("./section5_11_detail");
var _Section512 = require("./section5_12");
var _Section512SeizureOther = require("./section5_12_seizure_other");
var _Section515 = require("./section5_15");
var _Section515Lfnc = require("./section5_15_LFNC");
var _Section515Day = require("./section5_15_day");
var _Section515IntitialState = require("./section5_15_intitial_state");
var _Section515NonInvasive = require("./section5_15_non_invasive");
var _Section515RequireO2 = require("./section5_15_require_o2");
var _Section515SeizureOther = require("./section5_15_seizure_other");
var _Section515Ventilator = require("./section5_15_ventilator");
var _Section52 = require("./section5_2");
var _Section52Lfnc = require("./section5_2_LFNC");
var _Section52Day = require("./section5_2_day");
var _Section52IntitialState = require("./section5_2_intitial_state");
var _Section52NonInvasive = require("./section5_2_non_invasive");
var _Section52Ventilator = require("./section5_2_ventilator");
var _Section53 = require("./section5_3");
var _Section53Days = require("./section5_3_days");
var _Section53Main = require("./section5_3_main");
var _Section54 = require("./section5_4");
var _Section55 = require("./section5_5");
var _Section56 = require("./section5_6");
var _Section56Days = require("./section5_6_days");
var _Section56Main = require("./section5_6_main");
var _Section57 = require("./section5_7");
var _Section58 = require("./section5_8");
var _Section59 = require("./section5_9");
var _Section5NormalNewborn = require("./section5_normal_newborn");
var _Section6 = require("./section6");
var _SectionProgress = require("./section_progress");
var _Termandcondition = require("./termandcondition");
var _User = require("./user");
var _UserConcurrence = require("./user_concurrence");
var _UserNotify = require("./user_notify");
var _UserToken = require("./user_token");
var _Users = require("./users");

function initModels(sequelize) {
  var Noti15D = _Noti15D(sequelize, DataTypes);
  var Noti1Y = _Noti1Y(sequelize, DataTypes);
  var Noti30D = _Noti30D(sequelize, DataTypes);
  var Admin = _Admin(sequelize, DataTypes);
  var ChatHistory = _ChatHistory(sequelize, DataTypes);
  var ForgetPasswordToken = _ForgetPasswordToken(sequelize, DataTypes);
  var Hospital = _Hospital(sequelize, DataTypes);
  var Patient = _Patient(sequelize, DataTypes);
  var PatientConcurrence = _PatientConcurrence(sequelize, DataTypes);
  var Pin = _Pin(sequelize, DataTypes);
  var Refer = _Refer(sequelize, DataTypes);
  var RegisterCriteria = _RegisterCriteria(sequelize, DataTypes);
  var RegisterProgress = _RegisterProgress(sequelize, DataTypes);
  var RegisterToken = _RegisterToken(sequelize, DataTypes);
  var Section1 = _Section1(sequelize, DataTypes);
  var Section2 = _Section2(sequelize, DataTypes);
  var Section2Gbs = _Section2Gbs(sequelize, DataTypes);
  var Section2AbnormalSerology = _Section2AbnormalSerology(sequelize, DataTypes);
  var Section2ComplicationDuringPregnancy = _Section2ComplicationDuringPregnancy(sequelize, DataTypes);
  var Section2IntrapartumComplication = _Section2IntrapartumComplication(sequelize, DataTypes);
  var Section2MeternalMedication = _Section2MeternalMedication(sequelize, DataTypes);
  var Section2Other = _Section2Other(sequelize, DataTypes);
  var Section3 = _Section3(sequelize, DataTypes);
  var Section4 = _Section4(sequelize, DataTypes);
  var Section51 = _Section51(sequelize, DataTypes);
  var Section510 = _Section510(sequelize, DataTypes);
  var Section510PiccLine = _Section510PiccLine(sequelize, DataTypes);
  var Section510Uac = _Section510Uac(sequelize, DataTypes);
  var Section510Uvc = _Section510Uvc(sequelize, DataTypes);
  var Section510Day = _Section510Day(sequelize, DataTypes);
  var Section510OtherCentralLine = _Section510OtherCentralLine(sequelize, DataTypes);
  var Section511 = _Section511(sequelize, DataTypes);
  var Section511Detail = _Section511Detail(sequelize, DataTypes);
  var Section512 = _Section512(sequelize, DataTypes);
  var Section512SeizureOther = _Section512SeizureOther(sequelize, DataTypes);
  var Section515 = _Section515(sequelize, DataTypes);
  var Section515Lfnc = _Section515Lfnc(sequelize, DataTypes);
  var Section515Day = _Section515Day(sequelize, DataTypes);
  var Section515IntitialState = _Section515IntitialState(sequelize, DataTypes);
  var Section515NonInvasive = _Section515NonInvasive(sequelize, DataTypes);
  var Section515RequireO2 = _Section515RequireO2(sequelize, DataTypes);
  var Section515SeizureOther = _Section515SeizureOther(sequelize, DataTypes);
  var Section515Ventilator = _Section515Ventilator(sequelize, DataTypes);
  var Section52 = _Section52(sequelize, DataTypes);
  var Section52Lfnc = _Section52Lfnc(sequelize, DataTypes);
  var Section52Day = _Section52Day(sequelize, DataTypes);
  var Section52IntitialState = _Section52IntitialState(sequelize, DataTypes);
  var Section52NonInvasive = _Section52NonInvasive(sequelize, DataTypes);
  var Section52Ventilator = _Section52Ventilator(sequelize, DataTypes);
  var Section53 = _Section53(sequelize, DataTypes);
  var Section53Days = _Section53Days(sequelize, DataTypes);
  var Section53Main = _Section53Main(sequelize, DataTypes);
  var Section54 = _Section54(sequelize, DataTypes);
  var Section55 = _Section55(sequelize, DataTypes);
  var Section56 = _Section56(sequelize, DataTypes);
  var Section56Days = _Section56Days(sequelize, DataTypes);
  var Section56Main = _Section56Main(sequelize, DataTypes);
  var Section57 = _Section57(sequelize, DataTypes);
  var Section58 = _Section58(sequelize, DataTypes);
  var Section59 = _Section59(sequelize, DataTypes);
  var Section5NormalNewborn = _Section5NormalNewborn(sequelize, DataTypes);
  var Section6 = _Section6(sequelize, DataTypes);
  var SectionProgress = _SectionProgress(sequelize, DataTypes);
  var Termandcondition = _Termandcondition(sequelize, DataTypes);
  var User = _User(sequelize, DataTypes);
  var UserConcurrence = _UserConcurrence(sequelize, DataTypes);
  var UserNotify = _UserNotify(sequelize, DataTypes);
  var UserToken = _UserToken(sequelize, DataTypes);
  var Users = _Users(sequelize, DataTypes);


  return {
    Noti15D,
    Noti1Y,
    Noti30D,
    Admin,
    ChatHistory,
    ForgetPasswordToken,
    Hospital,
    Patient,
    PatientConcurrence,
    Pin,
    Refer,
    RegisterCriteria,
    RegisterProgress,
    RegisterToken,
    Section1,
    Section2,
    Section2Gbs,
    Section2AbnormalSerology,
    Section2ComplicationDuringPregnancy,
    Section2IntrapartumComplication,
    Section2MeternalMedication,
    Section2Other,
    Section3,
    Section4,
    Section51,
    Section510,
    Section510PiccLine,
    Section510Uac,
    Section510Uvc,
    Section510Day,
    Section510OtherCentralLine,
    Section511,
    Section511Detail,
    Section512,
    Section512SeizureOther,
    Section515,
    Section515Lfnc,
    Section515Day,
    Section515IntitialState,
    Section515NonInvasive,
    Section515RequireO2,
    Section515SeizureOther,
    Section515Ventilator,
    Section52,
    Section52Lfnc,
    Section52Day,
    Section52IntitialState,
    Section52NonInvasive,
    Section52Ventilator,
    Section53,
    Section53Days,
    Section53Main,
    Section54,
    Section55,
    Section56,
    Section56Days,
    Section56Main,
    Section57,
    Section58,
    Section59,
    Section5NormalNewborn,
    Section6,
    SectionProgress,
    Termandcondition,
    User,
    UserConcurrence,
    UserNotify,
    UserToken,
    Users,
  };
}
module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;
