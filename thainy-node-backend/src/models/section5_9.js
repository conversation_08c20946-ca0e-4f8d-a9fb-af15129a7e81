const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section59', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    RDS: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    pulmonary_air_leak: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    pneumothorax: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PIE: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    pneumomediastinum: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other_detail: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    death: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    death_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    death_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    death_day2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    death_reason: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PNA_start_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PNA_end_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PNA_diagnosis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PNA_nasal: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_start_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_end_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_diagnosis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_intubation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_CPAP: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_NC_1: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_NC_2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PMA_O2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    old_definition: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    old_definition_severe: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    new_definition: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    new_definition_severe: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PPHN: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PPHN_require_pulmonary_vasodilator: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PDA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PDA_require_medical_ligation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PDA_require_surgical_ligation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PDA_require_support_care: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NEC: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NEC_require_surgical_treatment: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    ROP: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    maximum_staging: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    treatment_laser: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    treatment_cryotherapy: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    treatment_anti_VEGF: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    treatment_no: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    IVH: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    maximum_grading: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    require_shunt: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PVL: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    SIP: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    SIP_require_exploratory_laparotomy: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    SIP_require_bedside_drainage: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    SIP_require_Other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    SIP_require_Other_note: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_9',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
