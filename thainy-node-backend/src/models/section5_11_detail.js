const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section511Detail', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    GBS: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    S_aureus: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    acinetobacter: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    ESBL: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    MRSA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    klebsiella: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    candida: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other_fungus: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    E_coli: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    CONS: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    pseudomonas: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    serratia: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    listeira: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_11_detail',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
          { name: "title" },
        ]
      },
    ]
  });
};
