const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  const Patient = sequelize.define('Patient', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    fullname: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    HN: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    infant_id: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    passport_id: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    illegal: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    illegal_id: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    illegal_hospital: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    DOB: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    sex: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    ethnic: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    mother_fullname: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    mother_id: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    mother_passport: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    mother_address: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    mother_tel: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    contact_person_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    relation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    contact_person_tel: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other_contact: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    created_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'patient',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });


  Patient.associate = function(models) {
    Patient.hasOne(models.RegisterCriteria, { foreignKey: 'TNR', sourceKey: 'TNR', as: 'register_criteria' });
    Patient.hasOne(models.SectionProgress, { foreignKey: 'TNR', sourceKey: 'TNR', as: 'section_progress' });
  };

  return Patient;
};
