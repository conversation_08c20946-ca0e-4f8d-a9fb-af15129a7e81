const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section6', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    discharge_status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_or_transfer: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    home_at_age: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    home_hospital_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    without_accessary: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    with_feeding_tube: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    with_oxygen: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    with_tracheostomy: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    transfer_hospital_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    transfer_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    transfer_at_age: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    transfer_hospital_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    post_transfer_disposition: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    post_discharge_home_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    post_without_accessary: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    post_with_feeding_tube: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    post_with_home_oxygen: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    post_with_tracheostomy: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    transfer_again_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    readmit_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    dead_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    dead_age: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    dead_hospital_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    lethal_congenital: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    immaturity: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    severe: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    IVH: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    pulmonary_hemorrhage: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NEC: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PIE_or_airleak_syndrome: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    BPD: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    extrame_prematurity: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    severe_RDS: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    immaturity_other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    severe_asphyxia: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PPHN: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    infection: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    cause_other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    unknow: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    '36_weeks': {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    birth_weight: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    length: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    length_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    head_circumference: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    head_circumference_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    discharge_growth_status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_weight: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_length: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_length_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_head_circumference: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_head_circumference_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_discharge_growth_status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    growth_status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_tft: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_cord_tsh: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_Cord_TSH: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_tsh_at: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_tsh_1: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_tsh_2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_ft4_at: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_ft4_1: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_ft4_2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_tsh: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_tsh_nm: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_tsh_ab: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_pku: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_pku_nm: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_pku_ab: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_hearing_test: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_hearing_test_oae: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_hearing_test_abr: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    hearing_test_other_01: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_hearing_test_other_01: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_hearing_test_type: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_jaundice: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_jaundice_1: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_jaundice_2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_cchd: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_cchd_type: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_cchd_ab: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_expanded_metabolic: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_expanded_metabolic_norm: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_screening_expanded_metabolic_ab: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other_1: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other_2: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other_3: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other_4: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other_5: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    screening_other_6: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section6',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
