const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section2AbnormalSerology', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    positive_anti_HIV: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    positive_HBsAg: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    positive_VDRL: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    normal_all: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section2_abnormal_serology',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
