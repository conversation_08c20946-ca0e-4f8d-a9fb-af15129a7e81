const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  const Pin = sequelize.define('Pin', {
    username: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    pin: {
      type: DataTypes.STRING(6),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'pin',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "username" },
        ]
      },
    ]
  });

  Pin.associate = function(models) {
    Pin.belongsTo(models.User, { foreignKey: 'username', as: 'user' });
  };

  return Pin;
};
