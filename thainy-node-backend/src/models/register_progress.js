const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('RegisterProgress', {
    TNR: {
      type: DataTypes.STRING(50),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    ID_Temporary: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    ID_Permanent: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(10),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'register_progress',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
        ]
      },
      {
        name: "TNR",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
        ]
      },
    ]
  });
};
