const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section5NormalNewborn', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    fdh_type: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    fdh_bm_reason: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fdh_bm_infant: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fdh_bm_prem: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    bm_formula_enriched: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    bm_formula_special: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fdh_formula_reason: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fdh_formula_infant: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fdh_formula_prem: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fo_only_enriched: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_fo_only_enriched: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    fo_only_special: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_fo_only_special: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_bm_formula_special: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    i_bm_formula_enriched: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_normal_newborn',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
