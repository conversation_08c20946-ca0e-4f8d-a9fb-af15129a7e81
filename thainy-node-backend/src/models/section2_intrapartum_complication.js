const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section2IntrapartumComplication', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    abnormal_vaginal_bleeding: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    MSAF: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    meternal_fever_or_Chrioaminonitis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    prolonged_rupture_of_membrane_more_than_18_hr: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    prolapsed_cord: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    IUGR: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    oilgohydramios: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    polyhydramios: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    no_complication: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section2_intrapartum_complication',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
