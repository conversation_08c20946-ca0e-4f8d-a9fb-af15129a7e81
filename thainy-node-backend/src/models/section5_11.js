const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section511', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    no_documented: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    CLASSI: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    proven_sepsis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    early_neonatal_sepsis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    late_neonatal_sepsis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    neonatal_menigitis: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    VAP: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    UTI: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_11',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
