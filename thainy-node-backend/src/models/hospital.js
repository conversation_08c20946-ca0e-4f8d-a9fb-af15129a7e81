const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Hospital', {
    hospital_number: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital_name: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'hospital',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "hospital_number" },
        ]
      },
    ]
  });
};
