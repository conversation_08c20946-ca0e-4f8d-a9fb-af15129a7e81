const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section510Day', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    UAC_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    UVC_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    PICC_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other_day: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_10_day',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
