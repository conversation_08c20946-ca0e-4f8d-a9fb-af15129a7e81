const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  const SectionProgress = sequelize.define('SectionProgress', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    section1: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section2: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section3: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section4: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_1: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_2: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_3: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_4: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_5: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_6: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_7: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_8: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_9: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_10: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_11: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_12: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_normal_newborn: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section5_sicknewborn: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    section6: {
      type: DataTypes.STRING(100),
      allowNull: false,
      defaultValue: "not done"
    },
    time: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section_progress',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });

  SectionProgress.associate = function(models) {
    SectionProgress.belongsTo(models.Patient, { foreignKey: 'TNR', targetKey: 'TNR', as: 'patient' });
  };

  return SectionProgress;
};
