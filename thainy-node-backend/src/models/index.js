const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const sequelize = require('./sequelize');

const db = {};

// Dynamically import and initialize all model files except index.js and sequelize.js
fs.readdirSync(__dirname)
  .filter(file =>
    file !== 'index.js' &&
    file !== 'sequelize.js' &&
    file.endsWith('.js')
  )
  .forEach(file => {
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  });

// If there are associations, call associate method
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

// Synchronize models with the database (for development only)
if (process.env.NODE_ENV !== 'production') {
  sequelize.sync({ alter: true })
    .then(() => {
      console.log('All models were synchronized successfully.');
    })
    .catch((err) => {
      console.error('Error synchronizing models:', err);
    });
}

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db; 