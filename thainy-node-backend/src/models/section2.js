const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section2', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    age: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    age_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    G: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    G_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    P: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    P_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    A: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    A_NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section2',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
