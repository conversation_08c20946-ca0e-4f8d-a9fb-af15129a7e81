const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section2ComplicationDuringPregnancy', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    no_ANC: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    over_DM_or_GDM: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    meternal_thyroid_disease: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    multiple_gestation: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    number: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    meternal_UTI: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    meternal_drug_abuse: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    other: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    NA: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    no_complication: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section2_complication_during_pregnancy',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
