const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Section515', {
    TNR: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    hospital: {
      type: DataTypes.STRING(100),
      allowNull: false,
      primaryKey: true
    },
    infection: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neonatal_sepsis: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    ns_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_neo_su: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_neo_pres: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    proven_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_aur: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_mrsa: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_cons: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_list: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_16: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_early_oth: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_sau: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_mrsa: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_cons: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_17: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_prov_late_oth: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    uti: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_18: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_18: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    in_uti_no_detect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    meningitis_cns: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_list: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_her: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_meni_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_19: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_19: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    meningitis_cns_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    pneumonia: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_list: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_rsv: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_flu: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_20: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_pneum_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    pneumonia_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    gi_infection: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_rota: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_21: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_gi_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    gi_infection_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    omphalitis: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_saur: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_mrsa: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_cons: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_22: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_omph_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    omphalitis_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_23: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_23: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_clabsi: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_presu: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_proven: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_meningitis_cns: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_vap: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_uti: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_35: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_saur: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_mrsa: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_cons: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_24: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_clabsi_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_saur: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_mrsa: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_cons: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_25: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_provn_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_list: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_herp: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_26: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_meni_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_meningitis_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_ser: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_list: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_rsv: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_flu: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_27: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_vap_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_vap_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_gbs: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_ecoli: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_kle: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_esbl: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_aci: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_pseud: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_enter: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_28_2: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    infect_hai_uti_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hai_uti_nodetect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    mtb_causes: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_mt_hypo: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_mt_hyper: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_mt_anem: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_mt_poly: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_11: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_mt_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rds: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rds: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_ttn: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_mas: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_oth_as: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    pphn: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_pphn: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_pneum: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    pals: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_pals_pneum: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_pals_pie: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_pals_pneumd: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_12: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_pals_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    uao: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_uao_lary: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_uao_pierr: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_uao_vocal: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_13: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_uao_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd_eff: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd_peri: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd_apn: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd_cns: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd_oth_cns: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_14: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_causes_rdmd_oth: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    chd: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_chd_chf: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_15: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Respiratory_dps: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_15: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    manage_require_support: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    initial_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    respiratory_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    require_o2: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    rdc_pals_manage_not_rq: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_35: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    low_birth_weight: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    moderate_lateprtm: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    sga: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    congenital: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_8: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_8: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_gene: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_small_causes_gene: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_multi: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_chro: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_small_causes_chro: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    maternal_drug_abuse: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_alc: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_smok: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_9: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes__ma_oth: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_pla: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_small_causes_pla: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_10: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    small_causes_unknown: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_low: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_allo: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_auto: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_cong: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_infect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_dic: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_asph: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_44: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thrombo_cause_unknown: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Thrombo_manage_plate: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Thrombo_manage_ivig: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_45: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Thrombo_manage_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Thrombo_manage_none: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anemia: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_tha: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_g6pd: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_red: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_feto: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_oth_blood: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_6: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes_unknown: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_manage_blood: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_manage_iron: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_7: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_manage_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_manage_none: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_low_hct: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    anem_causes: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    polycythemia: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_Highest_Hct: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_causes_intra: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_causes_twin: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_causes_delay: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_causes_infant: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_5: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_causes_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_causes_unknown: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    poly_require: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neonatal: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_Highest_TB: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_blood: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_tha: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_g6pd: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_red: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_poly: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_cep: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_non_feed: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_milk: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_jaun: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_incon: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_con: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_cho: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_hepa: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_infect: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_3: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_causes_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_acute: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_manage_photo: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_manage_total: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_4: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_manage_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neo_manage_none: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_iv_fluid: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_2: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_manage_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypoglycemia: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_low_blood: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_causes_low: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_causes_hyper: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_causes_in: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_causes_i_fail: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_1: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_manage_early: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_manage_iv: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    hypo_causes_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neonatal_seizure: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    metabolic_causes: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_meta_inb: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_neos_meta_inb: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_meta_hypog: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_meta_hypon: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_meta_hypoc: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_meta_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_cns: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_cong: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    intracranial: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_intrac_ivh: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_intrac_subd: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_intrac_suba: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_intrac_irh: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_29: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_intrac_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_30: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    i_other_30: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    received_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_rec_pheno: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_rec_pheny: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_rec_mid: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_rec_leve: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_rec_top: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_rec_diaz: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_31: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    aeeg_eeg_type: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_aeeg_norm: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_aeeg_ab: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    neos_aeeg_seiz: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_32: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_trauma: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_brach: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_fract: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_ceph: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    subgaleal: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_sub_notrq: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_sub_rq: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_sub_rqb: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_inj: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_33: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    birth_type_other: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_con_disease: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_con_cause: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_con_manage: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    other_con_note: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    status: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_username: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    last_modified_date: {
      type: DataTypes.STRING(100),
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'section5_15',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "TNR" },
          { name: "hospital" },
        ]
      },
    ]
  });
};
