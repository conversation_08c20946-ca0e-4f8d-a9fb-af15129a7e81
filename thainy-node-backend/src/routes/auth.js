const express = require('express');
const router = express.Router();

const { login, refreshAccessToken, logout, forgotPassword, checkPassword, changePassword } = require('../controllers/authController');
const authenticateToken = require('../middlewares/auth');

// Auth routes
router.post('/login', login);
router.post('/refresh', refreshAccessToken);
router.post('/logout', logout);
router.post('/forgot-password', forgotPassword);

router.post('/check-password', checkPassword);
router.post('/change-password', authenticateToken, changePassword);

router.get('/protected', authenticateToken, (req, res) => {
  res.json({
    message: 'This is a protected route',
    user: req.user, 
  });
});

module.exports = router;
