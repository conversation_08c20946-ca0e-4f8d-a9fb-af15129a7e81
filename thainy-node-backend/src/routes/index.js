const express = require('express');
const router = express.Router();
const healthRoute = require('./health');
const authRoutes = require('./auth');
const userRoutes = require('./user');
const patientRoutes = require('./patient');
const referRoutes = require('./refer');
const registerCriteriaRoutes = require('./registerCriteria');

router.use('/health', healthRoute);
router.use('/auth', authRoutes);
router.use('/user', userRoutes);
router.use('/patients', patientRoutes);
router.use('/refer', referRoutes);
router.use('/register-criteria', registerCriteriaRoutes);

module.exports = router;