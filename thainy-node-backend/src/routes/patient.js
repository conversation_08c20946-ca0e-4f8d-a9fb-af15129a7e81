const express = require('express');
const router = express.Router();
const { getAllPatients, searchPatients, getPatientByTnrAndHospital, getSectionProgress, getRegisterProgress, getReferCount, cancelRefer, updateReferToReject2, setPatientInactive, generateTNR, createPatient, validateInfantId, saveRegisterProgress } = require('../controllers/patientController');
const authenticateToken = require('../middlewares/auth');

router.get('/patients-list', authenticateToken, getAllPatients);
router.post('/search', authenticateToken, searchPatients);
router.post('/by-tnr-hospital', getPatientByTnrAndHospital);
router.post('/section-progress', getSectionProgress);
router.post('/register-progress', getRegisterProgress);
router.post('/refer-count', getReferCount);
router.post('/cancel-refer', cancelRefer);
router.post('/reject2-refer', updateReferToReject2);
router.post('/set-inactive', authenticateToken, setPatientInactive);
router.post('/generate-tnr', authenticateToken, generateTNR);
router.post('/create', authenticateToken, createPatient);
router.post('/validate-infant-id', validateInfantId);
router.post('/create-register-progress', authenticateToken, saveRegisterProgress);

module.exports = router; 