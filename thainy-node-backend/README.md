# ThainyBackend

## Project Setup

This is a Node.js backend project using <PERSON>, <PERSON><PERSON><PERSON> (with MySQL), <PERSON> for logging, and <PERSON><PERSON><PERSON> for code formatting.

### Prerequisites
- Node.js (v14+ recommended)
- MySQL server

### Installation
1. Clone the repository and navigate to the project directory.
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure your database credentials in `index.js` (replace `'database'`, `'username'`, `'password'` with your MySQL details).

### Running the Server
```bash
node index.js
```

The server will start on port 3000 by default.

### Logging
- Logs are stored in the `logs/app.log` file and also output to the console.

### Code Formatting
- Prettier is configured. To format your code, run:
  ```bash
  npx prettier --write .
  ``` 