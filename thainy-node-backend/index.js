require('dotenv').config();
const sequelize = require('./src/models/sequelize');
const app = require('./src/app');
const logger = require('./src/utils/logger');

const PORT = process.env.PORT || 4004;

// Test the Sequelize connection
sequelize.authenticate()
  .then(() => {
    logger.info('Sequelize connection established successfully.');
    app.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
    });
  })
  .catch((err) => {
    logger.error('Unable to connect to the database with Sequelize:', err);
  });
