/**
 * Simple test for the generic export utility
 * Run with: node test_generic_export.js
 */

const { exportData, formatData } = require('./src/utils/genericExport');
const fs = require('fs');

async function testGenericExport() {
  console.log('🧪 Testing Generic Export Utility...\n');

  // Sample test data
  const testPatients = [
    {
      TNR: '20250300001',
      fullname: '<PERSON>',
      DOB: '1990-01-01',
      sex: 'Male',
      hospital: 'Hospital_A',
      status: 'active',
      created_date: '2025-01-15 10:30:00'
    },
    {
      TNR: '20250300002',
      fullname: '<PERSON>',
      DOB: '1985-05-15',
      sex: 'Female',
      hospital: 'Hospital_B',
      status: 'active',
      created_date: '2025-01-16 14:20:00'
    },
    {
      TNR: '20250300003',
      fullname: '<PERSON>',
      DOB: '1992-12-03',
      sex: 'Male',
      hospital: 'Hospital_A',
      status: 'inactive',
      created_date: '2025-01-17 09:15:00'
    }
  ];

  const testPatientSections = {
    patient: {
      TNR: '20250300001',
      fullname: '<PERSON>',
      hospital: 'Hospital_A'
    },
    section1: {
      admission_status: 'Born in hospital',
      admission_date: '2025-01-15',
      discharge_date: '2025-01-20',
      stay_day: '5'
    },
    section2: {
      age: '30',
      G: '2',
      P: '1',
      A: '0'
    }
  };

  try {
    console.log('1. Testing JSON Export...');
    const jsonResult = await exportData(testPatients, { format: 'json' });
    console.log('✅ JSON export successful');
    console.log('Sample JSON output:', jsonResult.substring(0, 200) + '...');

    console.log('\n2. Testing CSV Export...');
    const csvResult = await exportData(testPatients, {
      format: 'csv',
      fields: ['TNR', 'fullname', 'DOB', 'sex', 'hospital', 'status']
    });
    console.log('✅ CSV export successful');
    console.log('Sample CSV output:');
    console.log(csvResult.split('\n').slice(0, 3).join('\n'));

    console.log('\n3. Testing PDF Export...');
    const pdfResult = await exportData(testPatients, {
      format: 'pdf',
      title: 'Patient List Report'
    });
    console.log('✅ PDF export successful');
    console.log(`PDF buffer size: ${pdfResult.length} bytes`);

    console.log('\n4. Testing Data Formatting...');
    const formattedData = formatData(testPatients, [
      'TNR',
      { key: 'fullname', label: 'Full Name' },
      { key: 'DOB', label: 'Date of Birth', format: 'date' },
      'sex',
      'hospital',
      { key: 'created_date', label: 'Created', format: 'datetime' }
    ]);
    console.log('✅ Data formatting successful');
    console.log('Sample formatted data:', formattedData[0]);

    console.log('\n5. Testing Patient Sections Export (PDF)...');
    const sectionsPdfResult = await exportData(testPatientSections, {
      format: 'pdf',
      title: 'ThaiNy Registry (TN) - Data Record Form - TNR: 20250300001'
    });
    console.log('✅ Patient sections PDF export successful');
    console.log(`Sections PDF buffer size: ${sectionsPdfResult.length} bytes`);

    console.log('\n6. Testing Single Object JSON Export...');
    const singleObjectResult = await exportData(testPatientSections.patient, {
      format: 'json'
    });
    console.log('✅ Single object export successful');

    console.log('\n7. Testing Error Handling...');
    try {
      await exportData(testPatients, { format: 'invalid_format' });
    } catch (error) {
      console.log('✅ Error handling works:', error.message);
    }

    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('- JSON Export: ✅ Working');
    console.log('- CSV Export: ✅ Working');
    console.log('- PDF Export: ✅ Working');
    console.log('- Data Formatting: ✅ Working');
    console.log('- Patient Sections: ✅ Working');
    console.log('- Error Handling: ✅ Working');

    console.log('\n🚀 Generic Export Utility is ready to use!');
    console.log('\nUsage in your controllers:');
    console.log('```javascript');
    console.log('const { exportAndSend } = require("../utils/genericExport");');
    console.log('');
    console.log('// In your controller:');
    console.log('await exportAndSend(res, patientData, {');
    console.log('  format: "csv",');
    console.log('  filename: "patients.csv",');
    console.log('  fields: ["TNR", "fullname", "DOB", "hospital"]');
    console.log('});');
    console.log('```');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testGenericExport();
