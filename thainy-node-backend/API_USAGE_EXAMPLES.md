# API Usage Examples

## Register Criteria API

### Create Register Criteria (Supports Both Formats)

**Endpoint**: `POST /register-criteria/create`

#### Option 1: Direct JSON Object (Recommended)

**Request Body**:
```json
{
  "TNR": "20250300001",
  "ward": "ICU",
  "pna": "PNA001",
  "patient_symptoms": "Respiratory distress, fever"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/register-criteria/create \
  -H "Content-Type: application/json" \
  -d '{
    "TNR": "20250300001",
    "ward": "ICU",
    "pna": "PNA001",
    "patient_symptoms": "Respiratory distress, fever"
  }'
```

#### Option 2: Legacy PHP Format (JSON String)

**Request Body**:
```json
{
  "isdata": "{\"TNR\":\"20250300001\",\"ward\":\"ICU\",\"pna\":\"PNA001\",\"patient_symptoms\":\"Respiratory distress, fever\"}"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/register-criteria/create \
  -H "Content-Type: application/json" \
  -d '{
    "isdata": "{\"TNR\":\"20250300001\",\"ward\":\"ICU\",\"pna\":\"PNA001\",\"patient_symptoms\":\"Respiratory distress, fever\"}"
  }'
```

#### Response (Same for Both Formats)

```json
{
  "TNR": "20250300001",
  "ward": "ICU",
  "pna": "PNA001",
  "patient_symptoms": "Respiratory distress, fever"
}
```

**PHP Equivalent**:
```php
<?php
   $data = $_POST["isdata"];
   $mydata = json_decode($data);
   add_to_database("register_criteria", $mydata);
   echo $mydata;
?>
```

---

## Patient Creation API

### Create Patient (JSON Object Format)

**Endpoint**: `POST /patients/create`

**Headers**:
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body** (JSON Object):

**Minimal Example** (required fields only - others will use defaults):
```json
{
  "TNR": "20250300001",
  "fullname": "John Doe",
  "HN": "HN001"
}
```

**Recommended Example** (with important fields):
```json
{
  "TNR": "20250300001",
  "fullname": "John Doe",
  "HN": "HN001",
  "DOB": "2025-01-15",
  "sex": "Male",
  "mother_fullname": "Jane Doe",
  "status": "active"
}
```

**Complete Example** (all fields):
```json
{
  "TNR": "20250300001",
  "fullname": "John Doe",
  "HN": "HN001",
  "infant_id": "INF001",
  "passport_id": "P123456",
  "illegal": "No",
  "illegal_id": "",
  "illegal_hospital": "",
  "DOB": "2025-01-15",
  "sex": "Male",
  "ethnic": "Asian",
  "mother_fullname": "Jane Doe",
  "mother_id": "M123456",
  "mother_passport": "MP123456",
  "mother_address": "123 Main St",
  "mother_tel": "************",
  "contact_person_name": "Emergency Contact",
  "relation": "Father",
  "contact_person_tel": "************",
  "other_contact": "Other info",
  "status": "active"
}
```

**Response** (Success):
```json
{
  "status": 1,
  "message": "Patient created successfully",
  "data": {
    "TNR": "20250300001",
    "hospital": "vernity",
    "created_date": "2025-07-23 10:30:45"
  }
}
```

**Response** (Error):
```json
{
  "status": 0,
  "message": "Error description"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/patients/create \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "TNR": "20250300001",
    "fullname": "John Doe",
    "HN": "HN001",
    "DOB": "2025-01-15",
    "sex": "Male",
    "mother_fullname": "Jane Doe",
    "status": "active"
  }'
```

**What the API does**:
1. Adds `created_date` timestamp to patient data
2. Inserts patient record into `patient` table
3. Updates `hospital` field for the TNR
4. Creates `section_progress` record with TNR, hospital, and timestamp
5. Returns "ok" on success

**PHP Equivalent**:
```php
<?php
   $TNR = $_POST["TNR"];
   $data = $_POST["mydata"];
   $mydata = json_decode($data);
   $created_date = date("Y-m-d H:i:s");
   array_push($mydata, $created_date);

   add_to_database("patient", $mydata);
   $hospital = $_SESSION["hospital"];

   mysql_query("UPDATE patient SET hospital = '$hospital' WHERE TNR = '$TNR' ");
   mysql_query("INSERT INTO section_progress (TNR,hospital,time) values ('$TNR','$hospital','$created_date') ");
   echo "ok";
?>
```

---

## TNR Generation API

### Generate TNR

**Endpoint**: `POST /patients/generate-tnr`

**Headers**: 
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "regist_date": "2025-03-15"
}
```

**Response** (Success):
```json
"20250300001"
```

**Response** (Failure):
```json
0
```

---

## Generic Database Utility

The `addToDatabase` function can be used for any table:

```javascript
const { addToDatabase } = require('../utils/databaseUtils');

// Example usage
const data = {
  TNR: "20250300001",
  ward: "ICU",
  pna: "PNA001", 
  patient_symptoms: "Respiratory distress"
};

const result = await addToDatabase('register_criteria', data);
```

### Supported Tables

The generic utility supports all major tables:
- `register_criteria`
- `patient`
- `patient_concurrence`
- `user_concurrence`
- `register_progress`
- `section1`, `section2`, `section3`, `section4`
- `section5_15_day`, `section5_1_year`, `section5_30_day`
- `section5_3_main`, `section5_4`, `section5_6_main`
- `section5_normal_newborn`
- `refer`
- `hospital`
- `user`
- `admin`
- And more...

---

## Patient Validation API

### Validate Patient by Infant ID

**Endpoint**: `POST /patients/validate-infant-id`

**Headers**:
```
Content-Type: application/json
```

**Request Body**:
```json
{
  "id": "1234567890123",
  "TNR": "20250300001"
}
```

**Response** (Success - No existing patient):
```json
{
  "status": 1,
  "message": "Patient validation successful - no existing patient found",
  "result": "pass"
}
```

**Response** (Error - Patient exists):
```json
{
  "status": 0,
  "message": "Patient with this infant ID already exists",
  "result": "error"
}
```

**What the API does**:
1. Checks if a patient with the given `infant_id` already exists in the `patient` table
2. If no patient found: Returns success with "pass" result
3. If patient exists: Deletes records from `patient_concurrence` table with the given TNR and returns error
4. This is used for validation before creating new patients to prevent duplicates

**PHP Equivalent**:
```php
<?php
  $id = $_POST["id"];
  $TNR = $_POST["TNR"];

  $query = mysql_query("SELECT * FROM `patient` WHERE `infant_id`= '".$id."'");

  if(mysql_num_rows($query)==0){
    echo "pass";
  }else{
    mysql_query("delete from patient_concurrence where TNR = '".$_POST["TNR"]."'");
    echo "Error";
  }
?>
```

---

## Register Progress API

### Create Register Progress

**Endpoint**: `POST /patients/create-register-progress`

**Headers**:
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "TNR": "20250300001",
  "temporary": "TEMP123456",
  "permanent": "PERM789012"
}
```

**Response** (Success):
```json
{
  "status": 1,
  "message": "Register progress created successfully",
  "data": {
    "TNR": "20250300001",
    "hospital": "Hospital_A",
    "temporary": "TEMP123456",
    "permanent": "PERM789012",
    "status": "done",
    "response": "TEMP123456  PERM789012"
  },
  "response": "TEMP123456  PERM789012"
}
```

**Response** (Error - Duplicate TNR):
```json
{
  "status": 0,
  "message": "Register progress for this TNR already exists."
}
```

**What the API does**:
1. Takes TNR, temporary ID, and permanent ID from request body
2. Gets hospital from authenticated user session
3. Determines status: "done" if permanent ID is provided, "not done" if empty
4. Inserts record into `register_progress` table with fields: TNR, hospital, ID_Temporary, ID_Permanent, status
5. Returns the temporary and permanent values concatenated (matching PHP response format)

**PHP Equivalent**:
```php
<?php
   $TNR = $_POST["TNR"];
   $temporary = $_POST["temporary"];
   $permanent = $_POST["permanent"];
   $mydata = [];
   array_push($mydata, $TNR);
   array_push($mydata, $hospital);
   array_push($mydata, $temporary);
   array_push($mydata, $permanent);
   if($permanent != ""){
     array_push($mydata, "done");
   }else{
     array_push($mydata, "not done");
   }

   add_to_database("register_progress", $mydata);
   echo $temporary."  ".$permanent;
?>
```

---

## PIN Management API

### Create or Update PIN

**Endpoint**: `POST /user/pin`

**Headers**:
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "pin": "123456"
}
```

**Response** (Success - PIN Created):
```json
{
  "status": 1,
  "message": "PIN created successfully",
  "data": {
    "username": "john_doe",
    "action": "created"
  }
}
```

**Response** (Success - PIN Updated):
```json
{
  "status": 1,
  "message": "PIN updated successfully",
  "data": {
    "username": "john_doe",
    "action": "updated"
  }
}
```

**Response** (Error - Invalid PIN):
```json
{
  "status": 0,
  "message": "PIN must be numeric and up to 6 digits"
}
```

**What the API does**:
1. Gets username from authenticated user session
2. Takes PIN from request body
3. Validates PIN format (numeric, up to 6 digits)
4. Uses `INSERT ... ON DUPLICATE KEY UPDATE` logic via Sequelize upsert
5. If username doesn't exist in pin table: Creates new record
6. If username already exists: Updates existing PIN
7. Returns success status with action performed

**PHP Equivalent**:
```php
<?php
 $username = $_SESSION["username"];
 $pin = $_POST["pin"];

 $query = mysql_query("INSERT INTO pin (username, pin) VALUES ('$username', '$pin') ON DUPLICATE KEY UPDATE pin = '$pin'");

 if($query == 0){
    echo json_encode(array("status" => 0));
    exit();
 }

 echo json_encode(array("status" => 1));
?>
```

---

## Error Responses

All APIs return consistent error responses:

```json
{
  "status": 0,
  "message": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (missing/invalid token)
- `404`: Not Found (resource doesn't exist)
- `409`: Conflict (duplicate key)
- `500`: Internal Server Error

---

## Testing the APIs

1. **Start the server**:
   ```bash
   npm run dev
   ```

2. **Test with cURL or Postman**

3. **For authenticated endpoints**, first login to get a JWT token:
   ```bash
   curl -X POST http://localhost:3000/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password"}'
   ```

4. **Use the token in subsequent requests**:
   ```bash
   curl -X POST http://localhost:3000/patients/generate-tnr \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"regist_date":"2025-03-15"}'
   ```
