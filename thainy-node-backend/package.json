{"name": "th<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "morgan": "^1.10.0", "mysql2": "^3.14.1", "nodemon": "^3.1.10", "sequelize": "^6.37.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"prettier": "^3.6.2", "sequelize-auto": "^0.8.8"}}