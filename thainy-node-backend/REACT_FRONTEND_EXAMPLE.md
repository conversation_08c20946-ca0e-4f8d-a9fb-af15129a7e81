# React Frontend Implementation for Patient Export API

## Problem Analysis

Your current response shows **raw text data** instead of a **PDF download**. This happens when the frontend doesn't handle the binary PDF response correctly.

## Correct React Implementation

### 1. **React Hook for Patient Export**

```jsx
import { useState } from 'react';

const usePatientExport = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const exportPatientData = async (TNR, sections = ['section1', 'section2', 'section3', 'section4']) => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('authToken'); // or however you store your JWT
      
      const response = await fetch('/api/patients/export-data', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          TNR,
          sections,
          format: 'pdf'
        })
      });

      if (!response.ok) {
        // Handle error response (JSON)
        const errorData = await response.json();
        throw new Error(errorData.message || 'Export failed');
      }

      // Handle success response (PDF binary)
      const blob = await response.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `patient_${TNR}_report.pdf`;
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return { success: true };

    } catch (err) {
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  return { exportPatientData, loading, error };
};

export default usePatientExport;
```

### 2. **React Component Example**

```jsx
import React, { useState } from 'react';
import usePatientExport from './hooks/usePatientExport';

const PatientExportComponent = ({ patientTNR }) => {
  const { exportPatientData, loading, error } = usePatientExport();
  const [selectedSections, setSelectedSections] = useState([
    'section1', 'section2', 'section3', 'section4'
  ]);

  const handleExport = async () => {
    const result = await exportPatientData(patientTNR, selectedSections);
    
    if (result.success) {
      alert('PDF exported successfully!');
    } else {
      alert(`Export failed: ${result.error}`);
    }
  };

  const handleSectionChange = (section, checked) => {
    if (checked) {
      setSelectedSections([...selectedSections, section]);
    } else {
      setSelectedSections(selectedSections.filter(s => s !== section));
    }
  };

  return (
    <div className="patient-export">
      <h3>Export Patient Report - TNR: {patientTNR}</h3>
      
      {error && (
        <div className="error-message" style={{ color: 'red', marginBottom: '10px' }}>
          Error: {error}
        </div>
      )}

      <div className="section-selection">
        <h4>Select Sections to Export:</h4>
        {['section1', 'section2', 'section3', 'section4', 'section5', 'section6'].map(section => (
          <label key={section} style={{ display: 'block', margin: '5px 0' }}>
            <input
              type="checkbox"
              checked={selectedSections.includes(section)}
              onChange={(e) => handleSectionChange(section, e.target.checked)}
              disabled={loading}
            />
            {section.toUpperCase()}
          </label>
        ))}
      </div>

      <button 
        onClick={handleExport} 
        disabled={loading || selectedSections.length === 0}
        style={{
          padding: '10px 20px',
          backgroundColor: loading ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginTop: '10px'
        }}
      >
        {loading ? 'Generating PDF...' : 'Export PDF'}
      </button>
    </div>
  );
};

export default PatientExportComponent;
```

### 3. **Alternative: Direct Function Approach**

```jsx
// Simple function approach (no hooks)
const exportPatientPDF = async (TNR, sections = ['section1', 'section2', 'section3', 'section4']) => {
  try {
    const token = localStorage.getItem('authToken');
    
    const response = await fetch('/api/patients/export-data', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ TNR, sections, format: 'pdf' })
    });

    if (response.ok) {
      // Success - handle PDF download
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `patient_${TNR}_report.pdf`;
      a.click();
      URL.revokeObjectURL(url);
      
      console.log('PDF downloaded successfully');
    } else {
      // Error - handle JSON error response
      const errorData = await response.json();
      console.error('Export failed:', errorData.message);
      alert(`Export failed: ${errorData.message}`);
    }
  } catch (error) {
    console.error('Export error:', error);
    alert(`Export error: ${error.message}`);
  }
};

// Usage in component
const MyComponent = () => {
  return (
    <button onClick={() => exportPatientPDF('20190100024', ['section1', 'section2'])}>
      Export Patient PDF
    </button>
  );
};
```

### 4. **With Loading State and Error Handling**

```jsx
import React, { useState } from 'react';

const PatientExportButton = ({ TNR, sections }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState(null);

  const handleExport = async () => {
    setIsExporting(true);
    setExportError(null);

    try {
      const token = localStorage.getItem('authToken');
      
      const response = await fetch('/api/patients/export-data', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          TNR,
          sections: sections || ['section1', 'section2', 'section3', 'section4'],
          format: 'pdf'
        })
      });

      if (response.ok) {
        // Success - download PDF
        const blob = await response.blob();
        
        // Check if it's actually a PDF
        if (blob.type === 'application/pdf' || blob.size > 0) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `patient_${TNR}_report.pdf`;
          link.click();
          URL.revokeObjectURL(url);
        } else {
          throw new Error('Invalid PDF response');
        }
      } else {
        // Error response
        const errorData = await response.json();
        throw new Error(errorData.message || 'Export failed');
      }
    } catch (error) {
      console.error('Export error:', error);
      setExportError(error.message);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div>
      <button 
        onClick={handleExport} 
        disabled={isExporting}
        style={{
          padding: '8px 16px',
          backgroundColor: isExporting ? '#6c757d' : '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isExporting ? 'not-allowed' : 'pointer'
        }}
      >
        {isExporting ? 'Exporting...' : 'Export PDF'}
      </button>
      
      {exportError && (
        <div style={{ color: 'red', marginTop: '5px', fontSize: '14px' }}>
          Error: {exportError}
        </div>
      )}
    </div>
  );
};

export default PatientExportButton;
```

## Key Points for Frontend Implementation

### ✅ **DO:**
1. **Use `response.blob()`** for PDF responses
2. **Check `response.ok`** before processing
3. **Handle both success (PDF) and error (JSON) responses**
4. **Create download link programmatically**
5. **Show loading states**
6. **Handle errors gracefully**

### ❌ **DON'T:**
1. **Don't use `response.text()`** for PDF - this causes the raw text issue you saw
2. **Don't use `response.json()`** for successful PDF responses
3. **Don't forget to cleanup URLs** with `URL.revokeObjectURL()`

## Testing Your Implementation

### 1. **Check Network Tab**
- Open browser DevTools → Network tab
- Click export button
- Look for the `/api/patients/export-data` request
- **Response should show binary data, not text**

### 2. **Check Response Headers**
- `Content-Type: application/pdf`
- `Content-Disposition: attachment; filename="patient_XXX_report.pdf"`

### 3. **Verify Download**
- PDF should download automatically
- File should open properly in PDF viewer
- Content should be formatted correctly (not raw text)

## Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| Raw text instead of PDF | Using `response.text()` | Use `response.blob()` |
| Empty/corrupted PDF | Server error not handled | Check `response.ok` first |
| No download triggered | Missing download link creation | Create `<a>` element programmatically |
| Memory leaks | Not cleaning up URLs | Use `URL.revokeObjectURL()` |

The key is to handle the **binary PDF response correctly** in your React frontend! 🎯
