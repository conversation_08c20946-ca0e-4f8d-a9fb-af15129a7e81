# Patient Export API Documentation

## Overview

This API provides the **exact same functionality** as your PHP export system, but in Node.js. It exports patient sections data as PDF files, following the same structure and logic as your original PHP code.

## API Endpoint

**POST** `/api/patients/export-data`

## Authentication

Requires JWT authentication token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Request Body

```json
{
  "TNR": "20250300001",
  "sections": ["section1", "section2", "section3", "section4"],
  "format": "pdf"
}
```

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `TNR` | string | Yes | - | Patient TNR (like `$_GET["TNR"]` in PHP) |
| `sections` | array | No | `["section1", "section2", "section3", "section4"]` | Sections to include (like `$_GET["s1"]`, `$_GET["s2"]` etc.) |
| `format` | string | No | `"pdf"` | Export format (currently only PDF supported) |

## Response

### Success Response
- **Content-Type**: `application/pdf`
- **Content-Disposition**: `attachment; filename="patient_{TNR}_report.pdf"`
- **Body**: PDF file binary data

### Error Response
```json
{
  "status": 0,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## PHP to Node.js Comparison

### Your PHP Code:
```php
$TNR = $_GET["TNR"];
$hospital = $_SESSION["hospital"];
$section1 = $_GET["s1"];
$section2 = $_GET["s2"];
// ... more sections

$pdf = new myPDF();
if($section1 == 'true') {
    $pdf->section1();
}
if($section2 == 'true') {
    $pdf->section2();
}
// ... more sections
$pdf->Output("S");
```

### Node.js Equivalent:
```javascript
// Request body
{
  "TNR": "20250300001",
  "sections": ["section1", "section2", "section3", "section4"],
  "format": "pdf"
}

// API automatically:
// 1. Gets TNR from request body (like $_GET["TNR"])
// 2. Gets hospital from JWT token (like $_SESSION["hospital"])
// 3. Includes requested sections (like $_GET["s1"] == 'true')
// 4. Generates PDF and sends to browser (like Output("S"))
```

## Usage Examples

### cURL Example
```bash
curl -X POST http://localhost:4004/api/patients/export-data \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "TNR": "20250300001",
    "sections": ["section1", "section2", "section3", "section4"],
    "format": "pdf"
  }' \
  --output patient_report.pdf
```

### JavaScript/Frontend Example
```javascript
async function exportPatientData(TNR, sections = ['section1', 'section2', 'section3', 'section4']) {
  try {
    const response = await fetch('/api/patients/export-data', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        TNR,
        sections,
        format: 'pdf'
      })
    });

    if (response.ok) {
      // Trigger file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `patient_${TNR}_report.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } else {
      const error = await response.json();
      console.error('Export failed:', error.message);
    }
  } catch (error) {
    console.error('Export error:', error);
  }
}

// Usage
exportPatientData('20250300001', ['section1', 'section2']);
```

### React Component Example
```jsx
import React, { useState } from 'react';

const PatientExport = ({ TNR }) => {
  const [loading, setLoading] = useState(false);
  const [selectedSections, setSelectedSections] = useState([
    'section1', 'section2', 'section3', 'section4'
  ]);

  const handleExport = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/patients/export-data', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          TNR,
          sections: selectedSections,
          format: 'pdf'
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `patient_${TNR}_report.pdf`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Export Patient Report - TNR: {TNR}</h3>
      
      <div>
        <h4>Select Sections:</h4>
        {['section1', 'section2', 'section3', 'section4', 'section5', 'section6'].map(section => (
          <label key={section}>
            <input
              type="checkbox"
              checked={selectedSections.includes(section)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedSections([...selectedSections, section]);
                } else {
                  setSelectedSections(selectedSections.filter(s => s !== section));
                }
              }}
            />
            {section.toUpperCase()}
          </label>
        ))}
      </div>

      <button onClick={handleExport} disabled={loading}>
        {loading ? 'Generating PDF...' : 'Export PDF'}
      </button>
    </div>
  );
};

export default PatientExport;
```

## Data Structure

The API fetches the same data as your PHP code:

### Patient Data
- Basic patient information from `patient` table

### Section Data
- **Section 1**: Admission information from `section1` table
- **Section 2**: Maternal information from multiple tables:
  - `section2` (main data)
  - `section2_abnormal_serology`
  - `section2_complication_during_pregnancy`
  - `section2_intrapartum_complication`
  - `section2_GBS`
  - `section2_meternal_medication`
- **Section 3**: Delivery information from `section3` table
- **Section 4**: Newborn information from `section4` table
- **Section 5**: From `section5` table
- **Section 6**: From `section6` table

## PDF Output

The generated PDF includes:
- **Header**: "ThaiNy Registry (TN) - Data Record Form"
- **TNR**: "TN Number: {TNR}"
- **Sections**: Each requested section with formatted data
- **Layout**: Similar to your PHP FPDF output

## Error Handling

| Status Code | Description |
|-------------|-------------|
| 200 | Success - PDF file returned |
| 400 | Bad Request - Missing TNR or hospital |
| 401 | Unauthorized - Invalid or missing JWT token |
| 404 | Not Found - Patient not found for given TNR and hospital |
| 500 | Internal Server Error - Database or processing error |

## Security

- **Authentication**: JWT token required (hospital extracted from token)
- **Authorization**: Users can only export patients from their hospital
- **Data Filtering**: Automatic filtering by hospital from JWT token
- **Input Validation**: TNR and sections validated

## Performance

- **Database Queries**: Optimized queries similar to your PHP code
- **Memory Usage**: PDF generated in memory and streamed to client
- **File Cleanup**: No temporary files created on server
- **Concurrent Requests**: Supports multiple simultaneous exports

## Testing

1. **Start your server:**
   ```bash
   npm run dev
   ```

2. **Test the API:**
   ```bash
   curl -X POST http://localhost:4004/api/patients/export-data \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"TNR": "20250300001", "sections": ["section1", "section2"]}' \
     --output test_export.pdf
   ```

3. **Verify the PDF:**
   - Check that the PDF downloads
   - Verify it contains the correct patient data
   - Compare with your PHP export output

## Migration Benefits

✅ **Same Functionality**: Identical to your PHP export
✅ **Better Performance**: More efficient than PHP FPDF
✅ **Modern API**: RESTful JSON API instead of GET parameters
✅ **Better Security**: JWT authentication instead of sessions
✅ **Scalable**: Can handle more concurrent requests
✅ **Maintainable**: Clean, documented code structure

The API is ready to use and provides the exact same functionality as your PHP export system! 🎉
